import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, IconCard, Tooltip, ConfirmDeleteModal } from '@/shared/components/common';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useCustomFields, useDeleteCustomField, useDeleteMultipleCustomFields } from '../hooks/useCustomFieldQuery';
import { CustomFieldQueryParams, CustomFieldListItem } from '../services/custom-field.service';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import CustomFieldForm from '../components/forms/CustomFieldForm';
import CustomFieldDetailForm from '../components/forms/CustomFieldDetailForm';

/**
 * Trang quản lý Custom Fields - Copy từ business module
 */
const CustomFieldsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  // State cho form và selection
  const [selectedCustomField, setSelectedCustomField] = useState<CustomFieldListItem | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [customFieldToDelete, setCustomFieldToDelete] = useState<CustomFieldListItem | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // Slide form hooks
  const {
    isVisible: isCreateVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  const {
    isVisible: isEditVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Mutations
  const deleteCustomFieldMutation = useDeleteCustomField();
  const deleteMultipleCustomFieldsMutation = useDeleteMultipleCustomFields();

  // Active filters
  const activeFilters = useActiveFilters();

  // Data table configuration
  const dataTableConfig = useDataTableConfig({
    columns: [
      {
        title: t('marketing:customField.form.fieldIdLabel', 'ID Trường'),
        dataIndex: 'configId',
        key: 'configId',
        sortable: true,
        width: 150,
        render: (configId: string) => (
          <span className="font-mono text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
            {configId}
          </span>
        ),
      },
      {
        title: t('marketing:customField.form.displayNameLabel', 'Tên hiển thị'),
        dataIndex: 'label',
        key: 'label',
        sortable: true,
        width: 200,
      },
      {
        title: t('marketing:customField.type', 'Loại'),
        dataIndex: 'type',
        key: 'type',
        sortable: true,
        width: 120,
        render: (type: string) => {
          const typeIcons = {
            text: 'type',
            number: 'hash',
            boolean: 'toggle-left',
            date: 'calendar',
            select: 'list',
            object: 'braces',
            array: 'brackets',
          };
          const typeColors = {
            text: 'blue',
            number: 'green',
            boolean: 'purple',
            date: 'orange',
            select: 'cyan',
            object: 'pink',
            array: 'indigo',
          };
          return (
            <div className="flex items-center space-x-2">
              <IconCard
                icon={typeIcons[type as keyof typeof typeIcons] || 'help-circle'}
                variant="default"
                size="sm"
                className={`text-${typeColors[type as keyof typeof typeColors] || 'gray'}-500`}
              />
              <span className="capitalize">{t(`marketing:customField.types.${type}`, type)}</span>
            </div>
          );
        },
      },
      {
        title: t('marketing:customField.form.description', 'Mô tả'),
        dataIndex: 'description',
        key: 'description',
        width: 300,
        render: (description: string) => (
          <Tooltip content={description || t('marketing:customField.noDescription', 'Không có mô tả')}>
            <span className="truncate max-w-xs block">
              {description || '-'}
            </span>
          </Tooltip>
        ),
      },
      {
        title: t('common:createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        key: 'createdAt',
        sortable: true,
        width: 150,
        render: (date: string) => new Date(date).toLocaleDateString('vi-VN'),
      },
      {
        title: t('common:actions', 'Thao tác'),
        key: 'actions',
        width: 100,
        render: (_, record: CustomFieldListItem) => (
          <div className="flex space-x-2">
            <Tooltip content={t('common:edit', 'Chỉnh sửa')}>
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => handleEditCustomField(record)}
                className="cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900"
              />
            </Tooltip>
            <Tooltip content={t('common:delete', 'Xóa')}>
              <IconCard
                icon="trash"
                variant="danger"
                size="sm"
                onClick={() => handleDeleteCustomField(record)}
                className="cursor-pointer hover:bg-red-50 dark:hover:bg-red-900"
              />
            </Tooltip>
          </div>
        ),
      },
    ],
  });

  const dataTable = useDataTable(dataTableConfig);

  // Query parameters
  const queryParams: CustomFieldQueryParams = useMemo(() => {
    const params: CustomFieldQueryParams = {
      page: dataTable.tableData.pagination.current,
      limit: dataTable.tableData.pagination.pageSize,
    };

    if (dataTable.tableData.searchTerm) {
      params.search = dataTable.tableData.searchTerm;
    }

    if (dataTable.tableData.sortField && dataTable.tableData.sortOrder) {
      params.sortBy = dataTable.tableData.sortField;
      params.sortDirection = dataTable.tableData.sortOrder === 'ascend' ? SortDirection.ASC : SortDirection.DESC;
    }

    return params;
  }, [dataTable.tableData]);

  // Fetch data
  const { data: customFieldsData, isLoading } = useCustomFields(queryParams);

  // Handlers
  const handleCreateCustomField = useCallback(() => {
    setSelectedCustomField(null);
    showCreateForm();
  }, [showCreateForm]);

  const handleEditCustomField = useCallback(
    (customField: CustomFieldListItem) => {
      setSelectedCustomField(customField);
      showEditForm();
    },
    [showEditForm]
  );

  const handleDeleteCustomField = useCallback((customField: CustomFieldListItem) => {
    setCustomFieldToDelete(customField);
    setShowDeleteConfirm(true);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    if (!customFieldToDelete) return;

    try {
      await deleteCustomFieldMutation.mutateAsync(customFieldToDelete.id);
      setShowDeleteConfirm(false);
      setCustomFieldToDelete(null);
    } catch (error) {
      console.error('Error deleting custom field:', error);
    }
  }, [customFieldToDelete, deleteCustomFieldMutation]);

  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setCustomFieldToDelete(null);
  }, []);

  // Bulk delete handlers
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) return;
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys]);

  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      const ids = selectedRowKeys.map(key => Number(key));
      await deleteMultipleCustomFieldsMutation.mutateAsync(ids);
      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);
    } catch (error) {
      console.error('Error bulk deleting custom fields:', error);
    }
  }, [selectedRowKeys, deleteMultipleCustomFieldsMutation]);

  const handleCreateSuccess = useCallback(() => {
    hideCreateForm();
  }, [hideCreateForm]);

  const handleEditSuccess = useCallback(() => {
    hideEditForm();
    setSelectedCustomField(null);
  }, [hideEditForm]);

  const handleCreateCancel = useCallback(() => {
    hideCreateForm();
  }, [hideCreateForm]);

  const handleEditCancel = useCallback(() => {
    hideEditForm();
    setSelectedCustomField(null);
  }, [hideEditForm]);

  return (
    <div className="w-full bg-background text-foreground">
      {/* Menu Icon Bar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleCreateCustomField}
        items={dataTable.menuItems}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete', 'Xóa nhiều'),
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Active Filters */}
      <ActiveFilters
        filters={activeFilters.filters}
        onRemoveFilter={activeFilters.removeFilter}
        onClearAll={activeFilters.clearFilters}
      />

      {/* Custom Fields Table */}
      <Card className="overflow-hidden">
        <Table<CustomFieldListItem>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={customFieldsData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: customFieldsData?.meta.currentPage || 1,
            pageSize: customFieldsData?.meta.itemsPerPage || 10,
            total: customFieldsData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Create Custom Field Form */}
      <SlideInForm isVisible={isCreateVisible}>
        <CustomFieldForm onSubmit={handleCreateSuccess} onCancel={handleCreateCancel} />
      </SlideInForm>

      {/* Edit Custom Field Form */}
      <SlideInForm isVisible={isEditVisible}>
        {selectedCustomField && (
          <CustomFieldDetailForm
            id={selectedCustomField.id}
            onSubmit={handleEditSuccess}
            onCancel={handleEditCancel}
          />
        )}
      </SlideInForm>

      {/* Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t('marketing:customField.confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa trường tùy chỉnh "{{name}}"?', {
          name: customFieldToDelete?.label,
        })}
      />

      {/* Bulk Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t('marketing:customField.confirmBulkDeleteMessage', 'Bạn có chắc chắn muốn xóa {{count}} trường tùy chỉnh đã chọn?', {
          count: selectedRowKeys.length,
        })}
      />
    </div>
  );
};

export default CustomFieldsPage;
