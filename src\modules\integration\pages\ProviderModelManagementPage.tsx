import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, ConfirmDeleteModal, Typography, Button, Icon, Container, Pagination } from '@/shared/components/common';

import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { ActiveFilters } from '@/modules/components/filters';

import useSlideForm from '@/shared/hooks/useSlideForm';

import {
  useProviderModels,
  useProviderModel,
  useCreateProviderModel,
  useUpdateProviderModel,
  useDeleteProviderModel,
  useDeleteMultipleProviderModels,
} from '../hooks/useProviderModel';
import {
  ProviderModelListItem,
  ProviderModel,
  CreateProviderModelDto,
  UpdateProviderModelDto,
  getProviderDisplayName,
  getProviderIcon,
} from '../types/provider-model.types';
import ProviderModelForm from '../components/ProviderModelForm';

/**
 * Trang quản lý Provider Model
 */
const ProviderModelManagementPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho các modal/form
  const [providerModelToEdit, setProviderModelToEdit] = useState<ProviderModel | null>(null);
  const [providerModelToDelete, setProviderModelToDelete] = useState<ProviderModelListItem | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedProviderModelId, setSelectedProviderModelId] = useState<string | null>(null);

  // State cho bulk delete
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // Hooks cho slide forms
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // State cho column visibility
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // State cho table data
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<string | null>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // API call để lấy danh sách provider models
  const queryParams = {
    page: currentPage,
    limit: pageSize,
    search: searchTerm || undefined,
    sortBy: sortBy || undefined,
    sortDirection: sortDirection,
  };

  const {
    data: providerModelsResponse,
    isLoading,
    error,
    refetch
  } = useProviderModels(queryParams);

  const providerModels = providerModelsResponse?.result?.items || [];
  const totalItems = providerModelsResponse?.result?.meta?.totalItems || 0;

  // Query single provider model for view/edit
  const {
    data: providerModelDetailResponse,

  } = useProviderModel(selectedProviderModelId || '');

  const providerModelDetail = providerModelDetailResponse?.result;

  // Mutations
  const createProviderModelMutation = useCreateProviderModel();
  const updateProviderModelMutation = useUpdateProviderModel();
  const deleteProviderModelMutation = useDeleteProviderModel();
  const deleteMultipleProviderModelsMutation = useDeleteMultipleProviderModels();

  const handleEditProviderModel = useCallback((providerModel: ProviderModelListItem) => {
    console.log('handleEditProviderModel called with providerModel:', providerModel);

    // Sử dụng dữ liệu từ card để đổ vào form edit
    // Chỉ cho phép sửa name và apiKey, không sửa provider type
    const providerModelForEdit = {
      id: providerModel.id,
      name: providerModel.name,
      type: providerModel.type,
      apiKey: '', // Không hiển thị API key cũ vì lý do bảo mật
      createdAt: providerModel.createdAt,
    };

    console.log('Provider model data for edit:', providerModelForEdit);
    setProviderModelToEdit(providerModelForEdit);
    showEditForm();
    console.log('Edit form opened with card data');
  }, [showEditForm]);

  // Effect để set data khi có providerModelDetail
  React.useEffect(() => {
    if (providerModelDetail && selectedProviderModelId) {
      if (isEditFormVisible && !providerModelToEdit) {
        setProviderModelToEdit(providerModelDetail);
      }
    }
  }, [providerModelDetail, selectedProviderModelId, isEditFormVisible, providerModelToEdit]);

  const handleDeleteProviderModel = useCallback((providerModel: ProviderModelListItem) => {
    setProviderModelToDelete(providerModel);
    setShowDeleteConfirm(true);
  }, []);

  // Handlers cho bulk delete
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys]);

  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      await deleteMultipleProviderModelsMutation.mutateAsync(selectedRowKeys as string[]);
      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);
      // Refetch data sau khi xóa thành công
      refetch();
    } catch (error) {
      console.error('Error deleting provider models:', error);
    }
  }, [selectedRowKeys, deleteMultipleProviderModelsMutation, refetch]);

  // Handlers cho search và filter
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang đầu khi search
  }, []);

  const handleClearSearch = useCallback(() => {
    setSearchTerm('');
    setCurrentPage(1);
  }, []);

  const handleClearSort = useCallback(() => {
    setSortBy(null);
    setSortDirection(SortDirection.DESC);
  }, []);

  const handleClearAll = useCallback(() => {
    setSearchTerm('');
    setSortBy(null);
    setSortDirection(SortDirection.DESC);
    setCurrentPage(1);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!providerModelToDelete) return;

    try {
      await deleteProviderModelMutation.mutateAsync(providerModelToDelete.id);
      setShowDeleteConfirm(false);
      setProviderModelToDelete(null);
      // Refetch data sau khi xóa thành công
      refetch();
    } catch (error) {
      console.error('Error deleting provider model:', error);
    }
  }, [providerModelToDelete, deleteProviderModelMutation, refetch]);

  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setProviderModelToDelete(null);
  }, []);

  // Xử lý submit form tạo mới
  const handleSubmitCreateProviderModel = useCallback(
    async (values: CreateProviderModelDto | UpdateProviderModelDto) => {
      try {
        setIsSubmitting(true);
        const createData = values as CreateProviderModelDto;

        await createProviderModelMutation.mutateAsync(createData);
        hideCreateForm();
        // Refetch data sau khi tạo thành công
        refetch();
      } catch (error) {
        console.error('Error creating provider model:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [createProviderModelMutation, hideCreateForm, refetch]
  );

  // Xử lý submit form chỉnh sửa
  const handleSubmitEditProviderModel = useCallback(
    async (values: CreateProviderModelDto | UpdateProviderModelDto) => {
      if (!providerModelToEdit) return;

      try {
        setIsSubmitting(true);
        const updateData = values as UpdateProviderModelDto;

        await updateProviderModelMutation.mutateAsync({
          id: providerModelToEdit.id,
          data: updateData,
        });
        hideEditForm();
        setProviderModelToEdit(null);
        // Refetch data sau khi cập nhật thành công
        refetch();
      } catch (error) {
        console.error('Error updating provider model:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [providerModelToEdit, updateProviderModelMutation, hideEditForm, refetch]
  );

  // Xử lý đóng form chỉnh sửa
  const handleCloseEditForm = useCallback(() => {
    hideEditForm();
    setTimeout(() => {
      setProviderModelToEdit(null);
      setSelectedProviderModelId(null);
    }, 300);
  }, [hideEditForm]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  return (
    <Container>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => window.history.back()}
            className="flex items-center space-x-2"
          >
            <Icon name="arrow-left" size="sm" />
            <span>{t('common.back', 'Quay lại')}</span>
          </Button>
          <Typography variant="h4">
            Tích hợp Nhà cung cấp Model
          </Typography>
        </div>
        <Typography variant="body1" color="muted" className="mt-2">
          Quản lý các Nhà cung cấp Model đã tích hợp
        </Typography>
      </div>
      <div className="space-y-6">
        {/* SlideInForm cho form tạo mới */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <ProviderModelForm
            onSubmit={handleSubmitCreateProviderModel}
            onCancel={hideCreateForm}
            isSubmitting={isSubmitting}
          />
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {(() => {
            console.log('SlideInForm render - isEditFormVisible:', isEditFormVisible, 'providerModelToEdit:', providerModelToEdit);
            return null;
          })()}
          {providerModelToEdit && (
            <ProviderModelForm
              initialData={providerModelToEdit}
              onSubmit={handleSubmitEditProviderModel}
              onCancel={handleCloseEditForm}
              isSubmitting={isSubmitting}
            />
          )}
        </SlideInForm>
        {/* Controls */}
        <div>
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => showCreateForm()}
            items={[
              {
                id: 'all',
                label: t('common:all'),
                icon: 'list',
                onClick: () => '',
              },
            ]}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
            additionalIcons={[
              {
                icon: 'trash',
                tooltip: t('common:bulkDelete', 'Xóa nhiều'),
                variant: 'primary',
                onClick: handleShowBulkDeleteConfirm,
                className: 'text-red-500',
                condition: selectedRowKeys.length > 0,
              },
            ]}
          />

          <ActiveFilters
            searchTerm={searchTerm}
            onClearSearch={handleClearSearch}
            sortBy={sortBy}
            sortDirection={sortDirection}
            onClearSort={handleClearSort}
            onClearAll={handleClearAll}
          />
        </div>

        {/* Provider Model Cards */}
        {error ? (
          <Card>
            <div className="p-12 text-center">
              <Icon name="circle-alert" size="xl" className="mx-auto text-red-400 dark:text-red-600 mb-4" />
              <Typography variant="h6" className="text-red-500 dark:text-red-400 mb-2">
                Lỗi tải dữ liệu
              </Typography>
              <Typography variant="body2" className="text-gray-400 dark:text-gray-500 mb-6">
                Có lỗi xảy ra khi tải danh sách Provider Model. Vui lòng thử lại.
              </Typography>
              <Button variant="outline" onClick={() => refetch()}>
                <Icon name="refresh-cw" size="sm" className="mr-2" />
                Thử lại
              </Button>
            </div>
          </Card>
        ) : isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 6 }).map((_, index) => (
              <Card key={index} className="animate-pulse">
                <div className="p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                    </div>
                  </div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                </div>
              </Card>
            ))}
          </div>
        ) : providerModels.length === 0 ? (
          <Card>
            <div className="p-12 text-center">
              <Icon name="robot" size="xl" className="mx-auto text-gray-400 dark:text-gray-600 mb-4" />
              <Typography variant="h6" className="text-gray-500 dark:text-gray-400 mb-2">
                {t('admin:integration.providerModel.empty.title', 'Chưa có Provider Model nào')}
              </Typography>
              <Typography variant="body2" className="text-gray-400 dark:text-gray-500 mb-6">
                {t('admin:integration.providerModel.empty.description', 'Thêm Provider Model để bắt đầu sử dụng.')}
              </Typography>
              <Button variant="primary" onClick={() => showCreateForm()}>
                <Icon name="plus" size="sm" className="mr-2" />
                {t('admin:integration.providerModel.actions.create', 'Tạo Provider Model')}
              </Button>
            </div>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {providerModels.map((providerModel) => (
              <Card key={providerModel.id} className="hover:shadow-lg transition-all duration-300">
                <div className="p-6">
                  <div className="flex items-center gap-4 mb-4">
                    {/* Provider Icon */}
                    <div className="w-12 h-12 rounded-lg bg-primary/10 dark:bg-primary/20 flex items-center justify-center">
                      <Icon
                        name={getProviderIcon(providerModel.type)}
                        size="lg"
                        className="text-primary dark:text-primary-400"
                      />
                    </div>

                    {/* Provider Info */}
                    <div className="flex-1 min-w-0">
                      <Typography variant="h6" className="font-semibold text-gray-900 dark:text-white truncate">
                        {providerModel.name}
                      </Typography>
                      <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                        {getProviderDisplayName(providerModel.type)}
                      </Typography>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleEditProviderModel(providerModel)}
                        className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                        title={t('admin:integration.providerModel.actions.edit', 'Chỉnh sửa')}
                      >
                        <Icon name="edit" size="sm" className="text-gray-600 dark:text-gray-400" />
                      </button>
                      <button
                        onClick={() => handleDeleteProviderModel(providerModel)}
                        className="p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                        title={t('admin:integration.providerModel.actions.delete', 'Xóa')}
                      >
                        <Icon name="trash" size="sm" className="text-red-500 dark:text-red-400" />
                      </button>
                    </div>
                  </div>

                  {/* Creation Date */}
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {t('common:createdAt', 'Tạo lúc')}: {new Date(Number(providerModel.createdAt)).toLocaleDateString('vi-VN')}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {!error && !isLoading && providerModels.length > 0 && totalItems > pageSize && (
          <div className="flex justify-center mt-6">
            <Pagination
              currentPage={currentPage}
              totalPages={Math.ceil(totalItems / pageSize)}
              onPageChange={setCurrentPage}
              showItemsPerPageSelector={false}
              variant="simple"
            />
          </div>
        )}
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('admin:integration.providerModel.confirmations.deleteTitle')}
        message={t('admin:integration.providerModel.confirmations.delete')}
        itemName={providerModelToDelete?.name}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={
          selectedRowKeys.length > 0
            ? t(
                'admin:integration.providerModel.confirmations.bulkDelete',
                `Bạn có chắc chắn muốn xóa ${selectedRowKeys.length} provider model đã chọn?`
              )
            : t('admin:integration.providerModel.confirmations.noItemsSelected', 'Không có item nào được chọn')
        }
      />
    </Container>
  );
};

export default ProviderModelManagementPage;
