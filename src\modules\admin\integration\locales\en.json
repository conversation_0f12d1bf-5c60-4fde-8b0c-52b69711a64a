{"integration": {"title": "Integration Management", "description": "Manage system integrations and connection configurations", "email": {"title": "Email Server Management", "description": "Manage email server configurations for automated email sending", "list": {"title": "Email Server List", "empty": "No email server configurations yet", "search": "Search by name or host...", "columns": {"serverName": "Server Name", "host": "Host", "port": "Port", "username": "Username", "ssl": "SSL", "status": "Status", "actions": "Actions"}}, "form": {"create": "Create Email Server", "edit": "Edit Email Server", "test": "Test Connection", "fields": {"serverName": "Server Name", "host": "Host", "port": "Port", "username": "Username", "password": "Password", "useSsl": "Use SSL", "useStartTls": "Use StartTLS", "additionalSettings": "Additional Settings (JSON)", "isActive": "Active", "recipientEmail": "Test Recipient Email", "subject": "Email Subject"}, "placeholders": {"serverName": "Enter server name...", "host": "smtp.gmail.com", "port": "587", "username": "<EMAIL>", "password": "Enter password...", "additionalSettings": "{}", "recipientEmail": "<EMAIL>", "subject": "Test Email"}}, "actions": {"create": "Create", "edit": "Edit", "delete": "Delete", "test": "Test", "save": "Save", "cancel": "Cancel", "close": "Close"}, "validation": {"serverName": {"required": "Server name is required", "maxLength": "Server name must not exceed 100 characters"}, "host": {"required": "Host is required", "maxLength": "Host must not exceed 255 characters"}, "port": {"min": "Port must be greater than 0", "max": "Port must be less than 65536"}, "username": {"required": "Username is required", "email": "Username must be a valid email"}, "password": {"required": "Password is required", "minLength": "Password must be at least 6 characters"}, "additionalSettings": {"invalidJson": "Additional settings must be valid JSON"}, "recipientEmail": {"email": "Recipient email must be a valid email"}, "subject": {"maxLength": "Subject must not exceed 200 characters"}}, "notifications": {"createSuccess": "Email server created successfully", "createError": "Error creating email server", "updateSuccess": "Email server updated successfully", "updateError": "Error updating email server", "deleteSuccess": "Email server deleted successfully", "deleteError": "Error deleting email server", "testSuccess": "Connection test successful", "testError": "Error testing connection"}, "confirmations": {"delete": "Are you sure you want to delete this email server?", "deleteTitle": "Confirm Delete"}}, "provider": {"title": "API keys Model Management", "description": "Manage AI providers and API key configurations", "error": {"title": "Data Loading Error", "description": "An error occurred while loading the Provider Model list. Please try again."}, "empty": {"title": "No Provider Models", "description": "Add Provider Models to get started."}, "list": {"title": "Provider Model List", "empty": "No provider models yet", "search": "Search by provider name...", "columns": {"name": "Provider Name", "type": "Type", "createdAt": "Created Date", "actions": "Actions"}}, "form": {"create": "Create Provider Model", "edit": "Edit Provider Model", "view": "View Provider Model", "createDescription": "Create new Provider Model to integrate with AI providers", "editDescription": "Edit Provider Model information. Only name and API key can be changed.", "viewDescription": "View detailed information of Provider Model", "fields": {"name": "Provider Model Name", "type": "Provider Type", "apiKey": "API Key"}, "placeholders": {"name": "Enter provider model name", "apiKey": "Enter API key", "apiKeyEdit": "Leave empty if you don't want to change API key..."}}, "actions": {"create": "Create Provider Model", "edit": "Edit", "view": "View Details", "delete": "Delete", "save": "Save", "cancel": "Cancel"}, "validation": {"name": {"required": "Provider name is required", "maxLength": "Provider name cannot exceed 255 characters"}, "type": {"required": "Provider type is required"}, "apiKey": {"required": "API key is required", "minLength": "API key must be at least 10 characters"}}, "notifications": {"createSuccess": "Provider model created successfully", "createError": "Error creating provider model", "updateSuccess": "Provider model updated successfully", "updateError": "Error updating provider model", "deleteSuccess": "Provider model deleted successfully", "deleteError": "Error deleting provider model"}, "confirmations": {"delete": "Are you sure you want to delete this provider model?", "deleteTitle": "Confirm Delete"}, "providers": {"OPENAI": "OpenAI", "ANTHROPIC": "Anthropic", "GOOGLE": "Google", "META": "Meta", "DEEPSEEK": "DeepSeek", "XAI": "XAI"}}, "sms": {"title": "SMS Server Management", "description": "Manage SMS server configurations for automated message sending", "list": {"title": "SMS Provider List", "empty": "No SMS provider configurations yet", "search": "Search by name or type...", "columns": {"name": "Configuration Name", "displayName": "Display Name", "type": "Provider Type", "status": "Status", "default": "<PERSON><PERSON><PERSON>", "actions": "Actions"}}, "actions": {"add": "Add SMS Provider", "edit": "Edit", "delete": "Delete", "view": "View Details", "test": "Test Connection", "activate": "Activate", "deactivate": "Deactivate"}, "status": {"active": "Active", "inactive": "Inactive", "error": "Error", "testing": "Testing", "pending": "Pending"}, "confirmations": {"deleteTitle": "Confirm Delete SMS Provider", "delete": "Are you sure you want to delete this SMS Provider? This action cannot be undone."}, "messages": {"createSuccess": "SMS Provider created successfully", "updateSuccess": "SMS Provider updated successfully", "deleteSuccess": "SMS Provider deleted successfully", "testSuccess": "Connection test successful", "createError": "Error creating SMS Provider", "updateError": "Error updating SMS Provider", "deleteError": "Error deleting SMS Provider", "testError": "Connection test failed"}}, "database": {"title": "Database Integration Management", "description": "Manage database connections for data integration system", "list": {"title": "Database Connections List", "empty": "No database connections yet", "emptyDescription": "Add your first database connection to start data integration", "search": "Search by name, host, database...", "columns": {"name": "Connection Name", "type": "Database Type", "host": "Host/Connection", "database": "Database", "status": "Status", "default": "<PERSON><PERSON><PERSON>", "actions": "Actions"}}, "actions": {"add": "Add Database Connection", "edit": "Edit", "delete": "Delete", "view": "View Details", "test": "Test Connection", "activate": "Activate", "deactivate": "Deactivate"}, "status": {"active": "Active", "inactive": "Inactive", "error": "Error", "testing": "Testing", "pending": "Pending"}, "form": {"createDescription": "Create a new database connection for the system", "editDescription": "Edit database connection information", "viewDescription": "View database connection details", "basicInfo": "Basic Information", "credentials": "Connection Credentials", "name": "Connection Name", "nameHelp": "Unique name to identify the connection", "namePlaceholder": "my_database_connection", "displayName": "Display Name", "displayNamePlaceholder": "My Database Connection", "type": "Database Type", "selectType": "Select database type", "description": "Description", "descriptionPlaceholder": "Description about this database connection...", "isDefault": "Set as default", "host": "Host", "port": "Port", "username": "Username", "password": "Password", "database": "Database", "schema": "<PERSON><PERSON><PERSON>", "connectionString": "Connection String", "connectionStringHelp": "Full MongoDB connection string", "collection": "Collection", "databaseIndex": "Database Index", "filePath": "File Path", "mode": "Mode", "readonly": "Read Only", "readwrite": "Read/Write", "create": "Create"}, "confirmations": {"deleteTitle": "Confirm Delete Database Connection", "delete": "Are you sure you want to delete this database connection? This action cannot be undone."}, "messages": {"createSuccess": "Database connection created successfully", "updateSuccess": "Database connection updated successfully", "deleteSuccess": "Database connection deleted successfully", "testSuccess": "Connection test successful", "createError": "Error creating database connection", "updateError": "Error updating database connection", "deleteError": "Error deleting database connection", "testError": "Connection test failed"}, "connectionInfo": "Connection Info", "databaseName": "Database Name", "lastTested": "Last Tested", "default": "<PERSON><PERSON><PERSON>", "noConnectionInfo": "No connection info"}, "payment": {"title": "Payment Gateway Management", "description": "Manage payment gateway configurations for the system"}, "social": {"title": "Social Media Management", "description": "Manage integration with social media platforms"}, "apiKeys": {"title": "API Keys Management", "description": "Manage API keys and security configurations"}, "webhooks": {"title": "Webhooks Management", "description": "Manage webhooks and integration with external systems"}}}