import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, Container, Icon, Loading } from '@/shared/components/common';
import Pagination from '@/shared/components/common/Pagination/Pagination';
import React, { useEffect, useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { TypeAgent, TypeAgentGrid, TypeAgentDetailView } from '../components/agent-add';
import { AgentConfigurationForm } from '../components/agent-config';
import { useGetTypeAgentsWithService } from '../hooks/useAgentService';
import {
    AgentConfigData,
    GetTypeAgentsQueryDto,
    SortDirection,
    TypeAgentSortBy
} from '../types';
import { mapTypeAgentsFromApi } from '../utils/api-mappers';
import { t } from 'i18next';

/**
 * Trang tạo Agent mới
 */
const AgentCreatePage: React.FC = () => {
    // State cho phân trang và filter
    const [page, setPage] = useState<number>(1);
    const [limit, setLimit] = useState<number>(15);
    const [search, setSearch] = useState<string>('');
    const [isSystem, setIsSystem] = useState<boolean | undefined>(undefined); // Mặc định lấy tất cả
    const [sortBy, setSortBy] = useState<TypeAgentSortBy>(TypeAgentSortBy.CREATED_AT);
    const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);
    const [totalItems, setTotalItems] = useState<number>(0);

    const navigate = useNavigate();

    // State để kiểm soát hiển thị màn hình cấu hình
    const [showConfigScreen, setShowConfigScreen] = useState<boolean>(false);

    // State để lưu dữ liệu cấu hình agent theo AGENT_USER_CREATION_GUIDE
    const [agentData, setAgentData] = useState<AgentConfigData & {
        // Các cờ để xác định component nào sẽ được hiển thị
        hasProfile?: boolean;
        hasModel?: boolean;
        hasIntegrations?: boolean;
        hasStrategy?: boolean;
        hasConvert?: boolean;
        hasResponse?: boolean;
        hasMultiAgent?: boolean;
        // Model selection scenarios (theo AGENT_USER_CREATION_GUIDE)
        model_base_id?: string | null;
        model_finetuning_id?: string | null;
        model_id?: string | null;
        provider_id?: string | null;
    }>({
        name: 'Tên agent',
        avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
        // Mặc định hiển thị tất cả các component
        hasProfile: true,
        hasModel: true,
        hasIntegrations: true,
        hasStrategy: true,
        hasConvert: true,
        hasResponse: true,
        hasMultiAgent: false,
        profile: {
            birthDate: '01/01/2000',
            gender: 'Nam',
            language: 'Tiếng Việt',
            education: 'Đại học',
            country: 'Việt Nam',
            position: 'Sales Assistant',
            skills: ['Tư vấn', 'Hỗ trợ khách hàng'],
            personality: 'Thân thiện, nhiệt tình, chuyên nghiệp',
        },
        modelConfig: {
            // Model configuration theo ModelConfigData interface
            provider: 'redai', // Mặc định sử dụng RedAI
            modelId: 'gpt-4', // Model mặc định
            vectorStore: 'pinecone',
            temperature: 0.7,
            topP: 0.9,
            topK: 40,
            maxTokens: 1000,
            instruction: ''
        },
        // Model selection scenarios (theo AGENT_USER_CREATION_GUIDE)
        model_base_id: null, // Scenario 1: Base Model
        model_finetuning_id: null, // Scenario 2: Fine-tuning Model
        model_id: null, // Scenario 3: Personal Model
        provider_id: null, // Scenario 3: Personal Model (đi cùng với model_id)
        integrations: {
            integrations: [
            ]
        },
        strategy: {
            strategyId: null // Mặc định không chọn strategy nào
        },
        response: {
            media: [],
            urls: [],
            products: []
        },
        convert: {
            fields: [
                {
                    id: 'field-1',
                    name: 'email',
                    description: 'Lấy tất cả email của người dùng',
                    enabled: true,
                    type: 'email',
                    required: true
                },
                {
                    id: 'field-2',
                    name: 'phone',
                    description: 'Lấy số điện thoại của người dùng',
                    enabled: true,
                    type: 'phone',
                    required: true
                },
                {
                    id: 'field-3',
                    name: 'name',
                    description: 'Lấy họ tên đầy đủ của người dùng',
                    enabled: true,
                    type: 'name',
                    required: true
                }
            ]
        },
        multiAgent: {
            agents: []
        }
    });

    // State để lưu loại agent được chọn
    const [selectedAgentType, setSelectedAgentType] = useState<number | null>(null);

    // State để lưu danh sách agent từ API
    const [agentTypes, setAgentTypes] = useState<TypeAgent[]>([]);

    // State để quản lý detail view
    const [showDetailView, setShowDetailView] = useState<boolean>(false);
    const [selectedTypeAgentForDetail, setSelectedTypeAgentForDetail] = useState<number | null>(null);

    // Tạo query params cho API
    const queryParams: GetTypeAgentsQueryDto = useMemo(() => ({
        page,
        limit,
        search: search || undefined,
        sortBy,
        sortDirection,
        isSystem,
    }), [page, limit, search, sortBy, sortDirection, isSystem]);

    // Gọi API để lấy danh sách type agents
    const {
        data: typeAgentsResponse,
        isLoading: isLoadingTypeAgents,
        error: typeAgentsError,
        refetch: refetchTypeAgents
    } = useGetTypeAgentsWithService(queryParams);

    // Cập nhật state khi có dữ liệu từ API
    useEffect(() => {
        if (typeAgentsResponse?.result?.items) {
            // Chuyển đổi dữ liệu từ API sang format frontend
            const mappedAgents = mapTypeAgentsFromApi(typeAgentsResponse.result.items);
            setAgentTypes(mappedAgents);
            setTotalItems(typeAgentsResponse.result.meta.totalItems);
        } else if (typeAgentsResponse?.result?.items === undefined && !isLoadingTypeAgents) {
            // Reset state khi không có dữ liệu
            setAgentTypes([]);
            setTotalItems(0);
        }
    }, [typeAgentsResponse, isLoadingTypeAgents]);

    // Removed: Form data logic for type agent creation/editing

    // Xử lý khi thay đổi trang
    const handlePageChange = (newPage: number) => {
        setPage(newPage);
    };

    // Xử lý khi thay đổi số lượng hiển thị trên một trang
    const handleLimitChange = (newValue: number) => {
        setLimit(newValue);
        setPage(1); // Reset về trang 1 khi thay đổi limit
    };

    // Xử lý khi tìm kiếm
    const handleSearch = (value: string) => {
        setSearch(value);
        setPage(1); // Reset về trang 1 khi tìm kiếm
    };

    // Tạo filter items theo cách của BlogListPage
    const filterItems = [
        {
            id: 'all-agents',
            label: isSystem === undefined ? '✓ Tất cả loại' : 'Tất cả loại',
            icon: 'users',
            onClick: () => {
                setIsSystem(undefined);
                setPage(1);
            }
        },
        {
            id: 'system-agents',
            label: isSystem === true ? '✓ System Agents' : 'System Agents',
            icon: 'shield',
            onClick: () => {
                setIsSystem(true);
                setPage(1);
            }
        },
        {
            id: 'user-agents',
            label: isSystem === false ? '✓ User Agents' : 'User Agents',
            icon: 'user',
            onClick: () => {
                setIsSystem(false);
                setPage(1);
            }
        },
        {
            id: 'divider-1',
            divider: true
        },
        {
            id: 'sort-name',
            label: sortBy === TypeAgentSortBy.NAME ? '✓ Sắp xếp theo tên' : 'Sắp xếp theo tên',
            icon: 'sort-alpha',
            onClick: () => {
                setSortBy(TypeAgentSortBy.NAME);
                setPage(1);
            }
        },
        {
            id: 'sort-date',
            label: sortBy === TypeAgentSortBy.CREATED_AT ? '✓ Sắp xếp theo ngày' : 'Sắp xếp theo ngày',
            icon: 'calendar',
            onClick: () => {
                setSortBy(TypeAgentSortBy.CREATED_AT);
                setPage(1);
            }
        },
        {
            id: 'divider-2',
            divider: true
        },
        {
            id: 'order-asc',
            label: sortDirection === SortDirection.ASC ? '✓ Tăng dần' : 'Tăng dần',
            icon: 'arrow-up',
            onClick: () => {
                setSortDirection(SortDirection.ASC);
                setPage(1);
            }
        },
        {
            id: 'order-desc',
            label: sortDirection === SortDirection.DESC ? '✓ Giảm dần' : 'Giảm dần',
            icon: 'arrow-down',
            onClick: () => {
                setSortDirection(SortDirection.DESC);
                setPage(1);
            }
        },
        {
            id: 'divider-3',
            divider: true
        },
        {
            id: 'reset-filters',
            label: 'Đặt lại bộ lọc',
            icon: 'refresh-cw',
            onClick: () => {
                setIsSystem(undefined);
                setSortBy(TypeAgentSortBy.CREATED_AT);
                setSortDirection(SortDirection.DESC);
                setPage(1);
            }
        }
    ];

    // Xử lý khi chọn loại agent
    const handleAgentTypeSelect = (agentId: number) => {
        setSelectedAgentType(agentId);

        // Cập nhật thông tin agent dựa trên loại agent đã chọn
        const selectedAgent = agentTypes.find(agent => agent.id === agentId);
        if (selectedAgent) {
            setAgentData(prev => ({
                ...prev,
                // Cập nhật các cờ hiển thị component dựa trên config
                hasProfile: selectedAgent.config.hasProfile,
                hasModel: true, // Luôn có model config
                hasIntegrations: selectedAgent.config.hasResources,
                hasStrategy: selectedAgent.config.hasStrategy,
                hasConvert: selectedAgent.config.hasConversion,
                hasResponse: selectedAgent.config.hasOutput,
                hasMultiAgent: selectedAgent.config.hasMultiAgent,
                profile: {
                    ...prev.profile,
                    name: selectedAgent.name,
                    position: `${selectedAgent.name.replace(' Agent', '')} AI`,
                    personality: selectedAgent.id === 1 ? 'Thân thiện, nhiệt tình, chuyên nghiệp' :
                        selectedAgent.id === 2 ? 'Tỉ mỉ, logic, chính xác' :
                            selectedAgent.id === 3 ? 'Thân thiện, hài hước, gần gũi' :
                                selectedAgent.id === 4 ? 'Tò mò, phân tích, chi tiết' :
                                    'Chuyên nghiệp, hiệu quả, chính xác'
                }
            }));
        }

        // Hiển thị màn hình cấu hình
        setShowConfigScreen(true);
    };

    // Removed: Custom agent creation logic

    // Xử lý khi xem chi tiết type agent
    const handleViewTypeAgent = (typeAgentId: number) => {
        setSelectedTypeAgentForDetail(typeAgentId);
        setShowDetailView(true);
    };

    // Xử lý khi đóng detail view
    const handleCloseDetailView = () => {
        setShowDetailView(false);
        setSelectedTypeAgentForDetail(null);
    };

    // Xử lý khi quay lại màn chọn loại agent
    const handleBackToTypeSelection = () => {
        // Quay lại màn hình chọn loại agent
        setShowConfigScreen(false);
        setSelectedAgentType(null);
    };

    return (
        <>
            {!showConfigScreen ? (
                // Màn hình chọn loại agent
                <Container>
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0 mb-4 sm:mb-6">
                        <div className="flex items-center gap-2">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.history.back()}
                                className="flex items-center space-x-2"
                            >
                                <Icon name="arrow-left" size="sm" />
                                <span>{t('common.back', 'Quay lại')}</span>
                            </Button>
                            <h1 className="text-xl sm:text-2xl font-bold">
                                {`Chọn loại Agent `}
                            </h1>
                        </div>
                    </div>

                    {/* Type Agent Detail View */}
                    <TypeAgentDetailView
                        typeAgentId={selectedTypeAgentForDetail}
                        isVisible={showDetailView}
                        onClose={handleCloseDetailView}
                    />

                    {/* Removed: Custom agent creation form */}
                    <Container>
                        <div className="py-4 sm:py-6">
                            <div className="p-3 sm:p-4">
                                <p className="text-gray-600 dark:text-gray-300 mb-4 sm:mb-6 text-sm sm:text-base">
                                    Chọn loại agent phù hợp với nhu cầu của bạn. Mỗi loại agent có những khả năng và đặc điểm khác nhau.
                                </p>

                                {/* Phần tìm kiếm và lọc */}
                                <MenuIconBar
                                    onSearch={handleSearch}
                                    items={filterItems}
                                    showDateFilter={false}
                                    showColumnFilter={false}
                                />

                                {/* Danh sách agent */}
                                <div className="overflow-x-auto">
                                    {isLoadingTypeAgents ? (
                                        <div className="flex justify-center items-center py-12">
                                            <Loading size="lg" />
                                        </div>
                                    ) : typeAgentsError ? (
                                        <div className="text-center py-12">
                                            <p className="text-red-500 mb-4">
                                                Có lỗi xảy ra khi tải danh sách Type Agent
                                            </p>
                                            <Button
                                                variant="outline"
                                                onClick={() => refetchTypeAgents()}
                                            >
                                                Thử lại
                                            </Button>
                                        </div>
                                    ) : (
                                        <TypeAgentGrid
                                            agents={agentTypes}
                                            selectedAgentId={selectedAgentType}
                                            onSelectAgent={handleAgentTypeSelect}
                                            onViewAgent={handleViewTypeAgent}
                                            showActions={true}
                                        />
                                    )}
                                </div>

                                {/* Phân trang - chỉ hiển thị khi có dữ liệu */}
                                {!isLoadingTypeAgents && !typeAgentsError && agentTypes.length > 0 && (
                                    <div className="mt-6 flex justify-end">
                                        <Pagination
                                            currentPage={page}
                                            borderless={true}
                                            totalItems={totalItems}
                                            itemsPerPage={limit}
                                            onPageChange={handlePageChange}
                                            onItemsPerPageChange={handleLimitChange}
                                            itemsPerPageOptions={[15, 23, 31, 79]}
                                            showItemsPerPageSelector={true}
                                            showPageInfo={true}
                                            variant="compact"
                                        />
                                    </div>
                                )}
                            </div>
                        </div>
                    </Container>
                </Container>
            ) : (
                // Màn hình cấu hình agent
                <AgentConfigurationForm
                    mode="create"
                    typeAgentId={selectedAgentType || undefined}
                    typeAgentConfig={agentTypes.find(agent => agent.id === selectedAgentType)?.config}
                    initialData={agentData}
                    onSuccess={(agentId) => {
                        console.log('Agent created successfully:', agentId);
                        navigate('/ai-agents');
                    }}
                    onBack={handleBackToTypeSelection}
                    onCancel={() => navigate('/ai-agents')}
                    availableAgents={agentTypes}
                />
            )}
        </>
    );
};

export default AgentCreatePage;
