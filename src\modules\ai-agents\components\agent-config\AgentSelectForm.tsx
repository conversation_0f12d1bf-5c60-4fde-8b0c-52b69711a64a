import { <PERSON><PERSON>, Card, Icon, Select, Textarea, Typography } from '@/shared/components/common';
import React, { useState } from 'react';
import { TypeAgent } from '../agent-add/TypeAgentCard';
import { MultiAgentItem } from '../../types/agent';

interface AgentSelectFormProps {
  /**
   * <PERSON>h sách các type agent có thể chọn
   */
  availableAgents: TypeAgent[];

  /**
   * Callback khi thêm agent mới
   */
  onAddAgent?: (agent: MultiAgentItem) => void;

  /**
   * Callback khi hủy
   */
  onCancel?: () => void;

  /**
   * Trạng thái hiển thị form
   */
  isVisible: boolean;
}

/**
 * Component form để chọn và cấu hình agent mới
 */
const AgentSelectForm: React.FC<AgentSelectFormProps> = ({
  availableAgents,
  onAddAgent,
  onCancel,
  isVisible
}) => {
  const [selectedAgentId, setSelectedAgentId] = useState<number | null>(null);
  const [customDescription, setCustomDescription] = useState('');

  // Reset form khi ẩn
  React.useEffect(() => {
    if (!isVisible) {
      setSelectedAgentId(null);
      setCustomDescription('');
    }
  }, [isVisible]);

  // Lấy thông tin agent được chọn
  const selectedAgent = availableAgents.find(agent => agent.id === selectedAgentId);

  // Xử lý thêm agent
  const handleAddAgent = () => {
    if (!selectedAgent) return;

    const newAgent: MultiAgentItem = {
      id: `agent_${Date.now()}_${selectedAgent.id}`,
      agentTypeId: selectedAgent.id,
      name: selectedAgent.name,
      description: selectedAgent.description,
      avatar: `https://randomuser.me/api/portraits/${selectedAgent.id % 2 === 0 ? 'men' : 'women'}/${selectedAgent.id}.jpg`,
      customDescription: customDescription.trim() || undefined
    };

    if (onAddAgent) {
      onAddAgent(newAgent);
    }

    // Reset form
    setSelectedAgentId(null);
    setCustomDescription('');
  };

  // Xử lý hủy
  const handleCancel = () => {
    setSelectedAgentId(null);
    setCustomDescription('');
    if (onCancel) {
      onCancel();
    }
  };

  if (!isVisible) return null;

  return (
    <Card className="p-4 mb-4 border-2 border-dashed border-gray-300 dark:border-gray-600">
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Icon name="plus" size="sm" className="text-blue-500" />
          <Typography variant="h6">Thêm Agent mới</Typography>
        </div>

        {/* Chọn loại agent */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Chọn loại Agent
          </label>
          <Select
            value={selectedAgentId?.toString() || ''}
            onChange={(value) => setSelectedAgentId(typeof value === 'string' && value ? parseInt(value) : null)}
            placeholder="Chọn một loại agent..."
            className="w-full"
            options={availableAgents.map((agent) => ({
              value: agent.id.toString(),
              label: `${agent.name} - ${agent.description}`
            }))}
          />
        </div>

        {/* Hiển thị thông tin agent được chọn */}
        {selectedAgent && (
          <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="flex items-start gap-3">
              <div className="w-10 h-10 rounded-full overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <Icon name={'user'} size="sm" className="text-white" />
              </div>
              <div className="flex-grow">
                <h4 className="font-medium text-gray-900 dark:text-white">
                  {selectedAgent.name}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {selectedAgent.description}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Mô tả tùy chỉnh */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Mô tả tùy chỉnh (tùy chọn)
          </label>
          <Textarea
            value={customDescription}
            onChange={(e) => setCustomDescription(e.target.value)}
            placeholder="Nhập mô tả tùy chỉnh cho agent này..."
            className="w-full"
            rows={3}
          />
          <p className="text-xs text-gray-500 mt-1">
            Nếu để trống, sẽ sử dụng mô tả mặc định của loại agent
          </p>
        </div>

        {/* Nút điều khiển */}
        <div className="flex gap-2 pt-2">
          <Button
            variant="primary"
            onClick={handleAddAgent}
            disabled={!selectedAgent}
            leftIcon={<Icon name="plus" size="sm" />}
          >
            Thêm Agent
          </Button>
          <Button
            variant="outline"
            onClick={handleCancel}
          >
            Hủy
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default AgentSelectForm;
