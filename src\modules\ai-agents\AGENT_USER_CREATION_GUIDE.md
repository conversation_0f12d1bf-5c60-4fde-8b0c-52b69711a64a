# Hướng dẫn đấu nối tạo Agent User theo Type Agent

## T<PERSON><PERSON> quan

Hệ thống Agent User đ<PERSON><PERSON><PERSON> thiết kế theo kiến trúc modular dựa trên **TypeAgent Configuration**. Mỗi TypeAgent có cấu hình riêng quyết định những khối (blocks) nào sẽ được hiển thị và validate khi tạo agent.

## Cấu trúc TypeAgent Configuration

```typescript
interface TypeAgentConfig {
  hasProfile: boolean;      // Khối Profile (thông tin cá nhân)
  hasOutput: boolean;       // Khối Output (kênh xuất bản)
  hasConversion: boolean;   // Khối Conversion (chuyển đổi)
  hasResources: boolean;    // Khối Resources (tài nguyên)
  hasStrategy: boolean;     // Khối Strategy (chiến lược)
  hasMultiAgent: boolean;   // Khối Multi-Agent (đa agent)
}
```

## Các loại TypeAgent phổ biến

### 1. **Basic Chatbot Agent**
```json
{
  "hasProfile": true,
  "hasOutput": false,
  "hasConversion": false,
  "hasResources": true,
  "hasStrategy": false,
  "hasMultiAgent": false
}
```

**Mô tả**: Agent cơ bản cho chat, chỉ cần profile và resources.

**Frontend cần hiển thị**:
- ✅ Profile Block (bắt buộc)
- ✅ Resources Block (bắt buộc)
- ❌ Output Block (ẩn)
- ❌ Conversion Block (ẩn)
- ❌ Strategy Block (ẩn)
- ❌ Multi-Agent Block (ẩn)

### 2. **Content Creator Agent**
```json
{
  "hasProfile": true,
  "hasOutput": true,
  "hasConversion": false,
  "hasResources": true,
  "hasStrategy": true,
  "hasMultiAgent": false
}
```

**Mô tả**: Agent tạo nội dung, cần output channels và strategy.

**Frontend cần hiển thị**:
- ✅ Profile Block (bắt buộc)
- ✅ Output Block (bắt buộc)
- ✅ Resources Block (bắt buộc)
- ✅ Strategy Block (bắt buộc)
- ❌ Conversion Block (ẩn)
- ❌ Multi-Agent Block (ẩn)

### 3. **E-commerce Assistant Agent**
```json
{
  "hasProfile": true,
  "hasOutput": true,
  "hasConversion": true,
  "hasResources": true,
  "hasStrategy": true,
  "hasMultiAgent": false
}
```

**Mô tả**: Agent hỗ trợ bán hàng, có conversion tracking.

**Frontend cần hiển thị**:
- ✅ Profile Block (bắt buộc)
- ✅ Output Block (bắt buộc)
- ✅ Conversion Block (bắt buộc)
- ✅ Resources Block (bắt buộc)
- ✅ Strategy Block (bắt buộc)
- ❌ Multi-Agent Block (ẩn)

### 4. **Enterprise Multi-Agent System**
```json
{
  "hasProfile": true,
  "hasOutput": true,
  "hasConversion": true,
  "hasResources": true,
  "hasStrategy": true,
  "hasMultiAgent": true
}
```

**Mô tả**: Hệ thống agent phức tạp với tất cả tính năng.

**Frontend cần hiển thị**:
- ✅ Profile Block (bắt buộc)
- ✅ Output Block (bắt buộc)
- ✅ Conversion Block (bắt buộc)
- ✅ Resources Block (bắt buộc)
- ✅ Strategy Block (bắt buộc)
- ✅ Multi-Agent Block (bắt buộc)

## API Flow

### 1. Lấy danh sách TypeAgent
```http
GET /api/v1/user/type-agents?page=1&limit=10&status=APPROVED
```

**Response**:
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "items": [
      {
        "id": 1,
        "name": "Basic Chatbot",
        "description": "Agent cơ bản cho chat",
        "config": {
          "hasProfile": true,
          "hasOutput": false,
          "hasConversion": false,
          "hasResources": true,
          "hasStrategy": false,
          "hasMultiAgent": false
        },
        "countTool": 3,
        "createdAt": 1672531200000
      }
    ],
    "meta": {
      "totalItems": 1,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

### 2. Tạo Agent User
```http
POST /api/v1/user/agents
```

**Request Body** (dựa trên TypeAgent config):
```json
{
  "name": "My Assistant",
  "typeId": 1,
  "modelConfig": {
    "temperature": 0.7,
    "top_p": 0.9,
    "top_k": 40,
    "max_tokens": 1000
  },
  "avatarMimeType": "image/jpeg",

  // Profile Block (nếu hasProfile = true)
  "profile": {
    "gender": "MALE",
    "dateOfBirth": 946659600000,
    "position": "Assistant AI",
    "education": "Đại học",
    "skills": ["Tư vấn", "Hỗ trợ khách hàng"],
    "personality": ["Chuyên nghiệp", "hiệu quả"],
    "languages": ["Tiếng Việt"],
    "nations": "Việt Nam"
  },

  // Output Block (nếu hasOutput = true)
  "output": {
    "facebookPageIds": ["page-1", "page-2"],
    "userWebsiteIds": ["website-1"]
  },

  // Resources Block (nếu hasResources = true)
  "resources": {
    "urlIds": ["url-1", "url-2"],
    "mediaIds": ["media-1", "media-2"],
    "productIds": ["1", "2", "3"]
  },

  // Strategy Block (nếu hasStrategy = true)
  "strategy": {
    "strategyId": "strategy-uuid",
    "config": {
      "approach": "consultative",
      "tone": "friendly"
    }
  },

  // Multi-Agent Block (nếu hasMultiAgent = true)
  "multiAgent": {
    "isEnabled": true,
    "coordinatorId": "coordinator-uuid",
    "subAgentIds": ["agent-1", "agent-2"]
  },

  // Model Configuration (3 scenarios)
  // Scenario 1: Base Model
  "model_base_id": "base-model-uuid",

  // Scenario 2: Fine-tuning Model
  // "model_finetuning_id": "finetuning-uuid",

  // Scenario 3: Personal Model
  // "model_id": "gpt-4o",
  // "provider_id": "provider-uuid"
}
```

## Frontend Implementation Guide

### 1. Component Structure
```typescript
// AgentCreationForm.tsx
interface AgentCreationFormProps {
  typeAgent: TypeAgent;
}

const AgentCreationForm: React.FC<AgentCreationFormProps> = ({ typeAgent }) => {
  const config = typeAgent.config;

  return (
    <form>
      {/* Basic Info - Always shown */}
      <BasicInfoSection />
      <ModelConfigSection />

      {/* Conditional Blocks based on TypeAgent config */}
      {config.hasProfile && <ProfileSection />}
      {config.hasOutput && <OutputSection />}
      {config.hasConversion && <ConversionSection />}
      {config.hasResources && <ResourcesSection />}
      {config.hasStrategy && <StrategySection />}
      {config.hasMultiAgent && <MultiAgentSection />}
    </form>
  );
};
```

### 2. Validation Logic
```typescript
// validation.ts
export const createAgentValidationSchema = (config: TypeAgentConfig) => {
  const schema = z.object({
    name: z.string().min(1, 'Tên agent không được để trống'),
    typeId: z.number(),
    modelConfig: z.object({
      temperature: z.number().min(0).max(2),
      top_p: z.number().min(0).max(1),
      top_k: z.number().min(0),
      max_tokens: z.number().min(1)
    }),

    // Conditional validation based on config
    ...(config.hasProfile && {
      profile: z.object({
        gender: z.enum(['MALE', 'FEMALE', 'OTHER']),
        position: z.string().min(1),
        // ... other profile fields
      })
    }),

    ...(config.hasOutput && {
      output: z.object({
        facebookPageIds: z.array(z.string()).optional(),
        userWebsiteIds: z.array(z.string()).optional()
      })
    }),

    ...(config.hasResources && {
      resources: z.object({
        urlIds: z.array(z.string()).optional(),
        mediaIds: z.array(z.string()).optional(),
        productIds: z.array(z.string()).optional()
      })
    })

    // ... other conditional validations
  });

  return schema;
};
```

### 3. State Management
```typescript
// useAgentCreation.ts
export const useAgentCreation = (typeAgent: TypeAgent) => {
  const [formData, setFormData] = useState<CreateAgentModularDto>({
    name: '',
    typeId: typeAgent.id,
    modelConfig: {
      temperature: 0.7,
      top_p: 0.9,
      top_k: 40,
      max_tokens: 1000
    },

    // Initialize conditional fields based on config
    ...(typeAgent.config.hasProfile && { profile: {} }),
    ...(typeAgent.config.hasOutput && { output: {} }),
    ...(typeAgent.config.hasResources && { resources: {} }),
    // ... other conditional initializations
  });

  const validationSchema = useMemo(
    () => createAgentValidationSchema(typeAgent.config),
    [typeAgent.config]
  );

  return {
    formData,
    setFormData,
    validationSchema,
    config: typeAgent.config
  };
};
```

## Backend Validation

Backend sẽ tự động validate dựa trên TypeAgent configuration:

1. **TypeAgentValidationHelper**: Validate dữ liệu theo config
2. **ModelValidationHelper**: Validate model configuration
3. **ProcessAgentBlocks**: Xử lý từng block theo config

## Error Handling

### Common Validation Errors:
- `AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND`: TypeAgent không tồn tại
- `AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED`: Model configuration không hợp lệ
- `AGENT_ERROR_CODES.PROFILE_REQUIRED`: Profile bắt buộc nhưng không được cung cấp
- `AGENT_ERROR_CODES.RESOURCES_INVALID`: Resources không hợp lệ

### Frontend Error Display:
```typescript
const handleSubmit = async (data: CreateAgentModularDto) => {
  try {
    await createAgent(data);
    // Success handling
  } catch (error) {
    if (error.code === 'AGENT_ERROR_CODES.PROFILE_REQUIRED') {
      // Highlight profile section
      scrollToSection('profile');
    }
    // Show error message
    showErrorMessage(error.message);
  }
};
```

## Testing

### Unit Tests:
- Test validation logic cho từng TypeAgent config
- Test conditional rendering của components
- Test form submission với different configs

### Integration Tests:
- Test full flow từ TypeAgent selection đến Agent creation
- Test error handling scenarios
- Test với different TypeAgent configurations

## Model Configuration Chi tiết

### 3 Scenarios Model Configuration:

#### Scenario 1: Base Model (System Model)
```json
{
  "model_base_id": "base-model-uuid",
  "model_finetuning_id": null,
  "model_id": null,
  "provider_id": null
}
```
- Sử dụng model có sẵn trong hệ thống
- Được quản lý bởi admin
- Stable và reliable

#### Scenario 2: Fine-tuning Model
```json
{
  "model_base_id": null,
  "model_finetuning_id": "finetuning-uuid",
  "model_id": null,
  "provider_id": null
}
```
- Model đã được fine-tune cho use case cụ thể
- Tốt hơn base model cho domain-specific tasks

#### Scenario 3: Personal Model (Provider Model)
```json
{
  "model_base_id": null,
  "model_finetuning_id": null,
  "model_id": "gpt-4o",
  "provider_id": "provider-uuid"
}
```
- User tự cấu hình model từ provider
- Cần có provider credentials
- Linh hoạt nhất nhưng cần setup

### Model Config Validation Rules:
- **Ít nhất 1 trong 3 scenarios** phải được cung cấp
- **model_id và provider_id** phải đi cùng nhau
- **Không được mix** các scenarios

## Troubleshooting Common Issues

### 1. Validation Error: "property modelId should not exist"

**Nguyên nhân**: Frontend gửi `modelId` trong `modelConfig` object, nhưng backend không expect field này.

**Giải pháp**:
```typescript
// ❌ SAI - Frontend gửi
{
  "modelConfig": {
    "modelId": "gpt-4.1-nano",  // <- Field này không được phép
    "temperature": 1,
    "top_p": 1,
    "top_k": 100,
    "max_tokens": 2334
  }
}

// ✅ ĐÚNG - Frontend nên gửi
{
  "modelConfig": {
    "temperature": 1,
    "top_p": 1,
    "top_k": 100,
    "max_tokens": 2334
  },
  // Model ID ở level root, không phải trong modelConfig
  "model_id": "gpt-4.1-nano",
  "provider_id": "provider-uuid"
}
```

### 2. TypeAgent Not Found Error

**Nguyên nhân**: TypeAgent ID không tồn tại hoặc không có status APPROVED.

**Giải pháp**:
- Kiểm tra TypeAgent có tồn tại: `GET /api/v1/user/type-agents/{id}`
- Đảm bảo status = APPROVED
- Refresh danh sách TypeAgent

### 3. Model Configuration Error

**Nguyên nhân**: Không cung cấp đủ thông tin model hoặc mix scenarios.

**Giải pháp**:
```typescript
// Frontend validation
const validateModelConfig = (data: CreateAgentModularDto) => {
  const hasBaseModel = !!data.model_base_id;
  const hasFinetuningModel = !!data.model_finetuning_id;
  const hasPersonalModel = !!(data.model_id && data.provider_id);

  // Phải có ít nhất 1 scenario
  if (!hasBaseModel && !hasFinetuningModel && !hasPersonalModel) {
    throw new Error('Phải chọn ít nhất một loại model');
  }

  // model_id và provider_id phải đi cùng nhau
  if ((data.model_id && !data.provider_id) || (!data.model_id && data.provider_id)) {
    throw new Error('model_id và provider_id phải được cung cấp cùng nhau');
  }
};
```

### 4. Profile Required Error

**Nguyên nhân**: TypeAgent có `hasProfile: true` nhưng frontend không gửi profile data.

**Giải pháp**:
```typescript
// Check TypeAgent config trước khi submit
if (typeAgent.config.hasProfile && !formData.profile) {
  throw new Error('Profile là bắt buộc cho loại agent này');
}
```

## Frontend Implementation Examples

### React Hook Form Integration:
```typescript
const AgentCreationForm = ({ typeAgent }: { typeAgent: TypeAgent }) => {
  const { control, handleSubmit, watch } = useForm<CreateAgentModularDto>({
    resolver: zodResolver(createAgentValidationSchema(typeAgent.config)),
    defaultValues: {
      name: '',
      typeId: typeAgent.id,
      modelConfig: {
        temperature: 0.7,
        top_p: 0.9,
        top_k: 40,
        max_tokens: 1000
      }
    }
  });

  const onSubmit = async (data: CreateAgentModularDto) => {
    try {
      // Remove modelId from modelConfig if exists
      if ('modelId' in data.modelConfig) {
        delete (data.modelConfig as any).modelId;
      }

      await createAgent(data);
    } catch (error) {
      handleError(error);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {/* Form fields based on typeAgent.config */}
    </form>
  );
};
```

### Model Selection Component:
```typescript
const ModelSelectionSection = ({ control, typeAgent }) => {
  const [modelType, setModelType] = useState<'base' | 'finetuning' | 'personal'>('base');

  return (
    <div>
      <RadioGroup value={modelType} onChange={setModelType}>
        <Radio value="base">Base Model (System)</Radio>
        <Radio value="finetuning">Fine-tuning Model</Radio>
        <Radio value="personal">Personal Model</Radio>
      </RadioGroup>

      {modelType === 'base' && (
        <Controller
          name="model_base_id"
          control={control}
          render={({ field }) => (
            <Select {...field} placeholder="Chọn Base Model">
              {/* Base model options */}
            </Select>
          )}
        />
      )}

      {modelType === 'finetuning' && (
        <Controller
          name="model_finetuning_id"
          control={control}
          render={({ field }) => (
            <Select {...field} placeholder="Chọn Fine-tuning Model">
              {/* Fine-tuning model options */}
            </Select>
          )}
        />
      )}

      {modelType === 'personal' && (
        <>
          <Controller
            name="model_id"
            control={control}
            render={({ field }) => (
              <Input {...field} placeholder="Model ID (e.g., gpt-4o)" />
            )}
          />
          <Controller
            name="provider_id"
            control={control}
            render={({ field }) => (
              <Select {...field} placeholder="Chọn Provider">
                {/* Provider options */}
              </Select>
            )}
          />
        </>
      )}
    </div>
  );
};
```

## Best Practices

1. **Always fetch TypeAgent config** trước khi render form
2. **Cache TypeAgent data** để tránh re-fetch
3. **Validate on client-side** trước khi submit
4. **Show clear error messages** cho từng validation rule
5. **Progressive disclosure** - chỉ hiển thị fields cần thiết
6. **Save draft** để user không mất dữ liệu khi navigate
7. **Loading states** cho tất cả async operations
8. **Clean modelConfig** - loại bỏ fields không mong muốn trước khi submit
9. **Model type selection** - UI rõ ràng cho 3 scenarios model
10. **Error boundary** - handle unexpected errors gracefully
