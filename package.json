{"name": "template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:staging": "vite --mode staging", "dev:production": "vite --mode production", "dev:testing": "vite --mode testing", "dev:localhost": "vite --mode localhost", "dev:host": "vite --host", "build": "npm run lint && npm run type-check:strict && vite build", "type-check:strict": "tsc --noEmit --strict", "build:staging": "npm run lint && npm run type-check:strict && vite build --mode staging", "build:production": "npm run lint && npm run type-check:strict && vite build --mode production", "build:testing": "npm run lint && npm run type-check:strict && vite build --mode testing", "build:localhost": "npm run lint && npm run type-check:strict && vite build --mode localhost", "build:widget": "vite build --config vite.widget.config.ts", "build:widget:production": "vite build --config vite.widget.config.ts --mode production", "preview:widget": "vite preview --config vite.widget.config.ts", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "preview": "vite preview", "preview:staging": "vite preview --mode staging", "preview:production": "vite preview --mode production", "preview:testing": "vite preview --mode testing", "preview:localhost": "vite preview --mode localhost", "preview:host": "vite preview --host", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,css,md}\"", "prebuild-disabled": "npm run type-check", "prebuild:staging": "npm run type-check", "prebuild:production": "npm run type-check", "prebuild:testing": "npm run type-check", "prebuild:localhost": "npm run type-check"}, "dependencies": {"@babel/plugin-transform-react-jsx": "7.27.1", "@eslint/js": "^9.22.0", "@floating-ui/react": "^0.27.8", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@hookform/resolvers": "^5.0.1", "@reduxjs/toolkit": "^2.7.0", "@tanstack/react-query": "^5.74.4", "@tanstack/react-query-devtools": "^5.74.4", "@types/dompurify": "^3.0.5", "@types/lodash": "^4.17.16", "@types/node": "^22.15.3", "@types/prismjs": "1.26.5", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-phone-number-input": "3.0.17", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-transition-group": "^4.4.12", "@types/uuid": "^10.0.0", "@types/xlsx": "0.0.35", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vitejs/plugin-react": "^4.3.4", "@xstate/react": "5.0.4", "@xyflow/react": "12.6.4", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "browser-image-compression": "^2.0.2", "clsx": "^2.1.1", "dagre": "0.8.5", "date-fns": "^4.1.0", "date-fns-tz": "3.2.0", "dompurify": "^3.2.5", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-i18n-json": "4.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-perf": "3.3.3", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-security": "3.0.1", "eslint-plugin-sonarjs": "3.0.2", "framer-motion": "^12.9.4", "globals": "^16.0.0", "i18next": "^25.0.2", "lodash": "^4.17.21", "lucide-react": "0.511.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prismjs": "1.30.0", "react": "^18.2.0", "react-colorful": "5.6.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-touch-backend": "16.0.1", "react-dom": "^18.2.0", "react-dropzone": "14.3.8", "react-grid-layout": "^1.5.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.2", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-phone-number-input": "3.4.12", "react-redux": "^9.2.0", "react-router-dom": "^7.5.3", "react-simple-code-editor": "0.14.1", "react-syntax-highlighter": "^15.6.1", "react-transition-group": "^4.4.5", "recharts": "^2.15.3", "redux-persist": "^6.0.0", "rollup-plugin-visualizer": "^5.14.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.3.5", "terser": "^5.39.2", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "uuid": "^11.1.0", "vite": "^6.3.1", "vite-plugin-checker": "^0.9.3", "vite-plugin-eslint": "^1.8.1", "xlsx": "0.18.5", "xstate": "5.19.3", "zod": "^3.24.4", "zod-to-json-schema": "3.24.5"}}