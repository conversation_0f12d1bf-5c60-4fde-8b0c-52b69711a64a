# Avatar Upload Flow - Customer Basic Info với Queue System

## Tổng quan

Đã implement flow upload avatar hoàn chỉnh với presigned URL từ backend và sử dụng TaskQueue system như MediaPage để upload lên cloud storage.

## Flow hoạt động

### 1. User chọn avatar file
```typescript
// User click "Thay đổi" → chọn file
handleAvatarUpload(event) {
  const file = event.target.files[0];
  
  // Validation
  if (!file.type.startsWith('image/')) return;
  if (file.size > 5MB) return;
  
  // Create preview
  const previewUrl = URL.createObjectURL(file);
  setFormData(prev => ({ ...prev, avatar: previewUrl }));
  
  // Store file for later upload
  setAvatarFile(file);
}
```

### 2. User click Save
```typescript
handleSave() {
  // Prepare data with avatar file metadata
  const validatedData = {
    name: "<PERSON>uyễn Văn A",
    phone: "0912345678",
    email: { primary: "<EMAIL>", secondary: "" },
    address: "123 ABC Street",
    avatarFile: {
      fileName: "avatar.jpg",
      mimeType: "image/jpeg"
    }
  };
  
  // Call mutation with actual file
  updateBasicInfoMutation.mutateAsync({
    id: customerId,
    data: validatedData,
    avatarFile: actualFileObject
  });
}
```

### 3. API Call với avatar metadata
```bash
PUT /user/convert-customers/1/basic-info
{
  "name": "Nguyễn Văn A",
  "phone": "0912345678",
  "email": {
    "primary": "<EMAIL>",
    "secondary": ""
  },
  "address": "123 Đường ABC, Quận 1, TP.HCM",
  "avatarFile": {
    "fileName": "avatar.jpg",
    "mimeType": "image/jpeg"
  }
}
```

### 4. Backend Response với Upload URL (Actual Format)
```json
{
  "code": 200,
  "message": "Cập nhật thông tin cơ bản khách hàng chuyển đổi thành công",
  "result": {
    "id": "19",
    "name": "NGUYEN NGOC HAI ANH",
    "phone": "0793355880",
    "email": {
      "primary": "<EMAIL>",
      "secondary": ""
    },
    "address": "Mộ cổ - Nguyên Xá - Minh Khai - Bắc Từ Liêm - Hà Nội",
    "avatar": "1/customer_avatars/2025/06/1748943626856-36ce3607-40a4-40a3-b27d-c6e8c4b2b8a5.jpg",
    "updatedAt": null,
    "avatarUpload": {
      "uploadUrl": "https://redaivn.hn.ss.bfcplatform.vn/1/customer_avatars/2025/06/1748943626856-36ce3607-40a4-40a3-b27d-c6e8c4b2b8a5.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&...",
      "publicUrl": "https://cdn.redai.vn/1/customer_avatars/2025/06/1748943626856-36ce3607-40a4-40a3-b27d-c6e8c4b2b8a5.jpg?expires=1748947226&signature=T_8Hhp8RtY6Zjinkw5G8Zr0K_Zk"
    }
  }
}
```

### 5. Frontend upload file lên cloud với TaskQueue
```typescript
// Trong useUpdateCustomerBasicInfo hook - sử dụng queue system như MediaPage
if (avatarFile && response.data.avatarUpload?.uploadUrl) {
  // Upload file lên presigned URL với TaskQueue
  await fileUploadWithQueue.uploadToUrlWithQueue({
    file: avatarFile,
    presignedUrl: response.data.avatarUpload.uploadUrl,
    taskTitle: `Upload avatar: ${avatarFile.name}`,
    taskDescription: `Kích thước: ${(avatarFile.size / 1024).toFixed(1)} KB`,
    onUploadProgress: (progress) => {
      console.log(`Avatar upload progress: ${progress}%`);
    },
  });
}
```

## UI Features (Simplified Interface)

### 1. **Avatar Preview**
- Hiển thị preview ngay khi chọn file
- Fallback to default avatar nếu không có

### 2. **Upload Indicators**
- 🔵 Blue upload icon khi có file mới chưa save
- 📝 Simple progress text: "Đang cập nhật thông tin..." khi save

### 3. **File Validation**
- ✅ Chỉ cho phép file ảnh (image/*)
- ✅ Max size 5MB
- ✅ Error notifications với NotificationUtil

### 4. **User Actions**
- 📤 "Thay đổi" button để chọn file mới
- 🗑️ "Xóa" button để remove avatar
- 💾 "Save" button để lưu tất cả thông tin

### 5. **TaskQueue Integration**
- Upload được xử lý bởi TaskQueue system
- Progress tracking trong TaskQueue panel
- Retry và error handling tự động

## Technical Implementation

### 1. **State Management (Simplified)**
```typescript
const [avatarFile, setAvatarFile] = useState<File | null>(null);
const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
```

### 2. **Hook Integration**
```typescript
const updateBasicInfoMutation = useUpdateCustomerBasicInfo();

// Sử dụng useCorsAwareFileUpload như MediaPage
const fileUploadWithQueue = useCorsAwareFileUpload({
  defaultTaskTitle: 'Upload avatar',
  autoAddToQueue: true,
});
```

### 3. **TaskQueue Upload Service**
- Sử dụng `useCorsAwareFileUpload` hook như MediaPage
- Upload với presigned URL từ backend
- TaskQueue xử lý progress tracking và retry logic

### 4. **Error Handling**
- File validation errors → NotificationUtil.error()
- Upload errors → Console log (không fail toàn bộ mutation)
- API errors → Standard error handling

## Backend Requirements

Backend cần implement:

1. **Accept avatar metadata** trong request body
2. **Generate presigned URL** cho S3/cloud storage
3. **Return upload URL** trong response
4. **Handle file upload completion** (optional webhook)

## Testing

### Test Cases:

1. **✅ Chọn file ảnh hợp lệ**
   - Preview hiển thị ngay
   - Upload icon xuất hiện
   - File name hiển thị

2. **✅ Validation errors**
   - File không phải ảnh → error notification
   - File > 5MB → error notification

3. **✅ Upload flow**
   - Click Save → API call với metadata
   - Backend trả về uploadUrl → upload file
   - Progress bar hiển thị 0-100%
   - Success → clear file state

4. **✅ Remove avatar**
   - Click trash icon → clear preview và file
   - Reset progress về 0

## Benefits

1. **🚀 Performance**: Upload trực tiếp lên cloud, không qua server
2. **📊 UX**: Real-time progress tracking
3. **🔒 Security**: Presigned URLs có thời hạn
4. **⚡ Scalability**: Không tải server với file uploads
5. **🎯 Reliability**: Separate upload step, không fail toàn bộ form nếu upload lỗi
