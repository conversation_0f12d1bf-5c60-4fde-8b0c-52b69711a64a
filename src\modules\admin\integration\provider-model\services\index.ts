/**
 * Admin Provider Model API Service
 */

import { apiClient } from '@/shared/api';
import { PaginatedResult, ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import {
  CreateProviderModelDto,
  ProviderModel,
  ProviderModelListItem,
  ProviderModelQueryParams,
  UpdateProviderModelDto,
} from '../types';

const API_BASE_URL = '/admin/system-key-llm';

/**
 * Service layer cho admin provider model operations
 * Cung cấp business logic và error handling
 */
export class AdminProviderModelService {
  /**
   * L<PERSON>y danh sách admin provider models với phân trang
   */
  static async getProviderModels(
    params: Partial<ProviderModelQueryParams> = {}
  ): Promise<PaginatedResult<ProviderModelListItem>> {
    try {
      // Validate parameters
      const validatedParams = {
        page: params.page || 1,
        limit: Math.min(params.limit || 10, 100), // Max 100 items per page
        search: params.search?.trim(),
        sortBy: params.sortBy,
        sortDirection: params.sortDirection,
        type: params.type,
      };

      // Remove undefined values
      const cleanParams = Object.fromEntries(
        Object.entries(validatedParams).filter(([, value]) => value !== undefined)
      );

      const response = await apiClient.get<PaginatedResult<ProviderModelListItem>>(
        API_BASE_URL,
        { params: cleanParams }
      );

      return response.result;
    } catch (error) {
      console.error('Service error - getProviderModels:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Lấy thông tin chi tiết một admin provider model
   */
  static async getProviderModel(
    id: string
  ): Promise<ProviderModel> {
    try {
      if (!id || id.trim() === '') {
        throw new Error('Provider model ID is required');
      }

      const response = await apiClient.get<ProviderModel>(
        `${API_BASE_URL}/${id.trim()}`
      );

      return response.result;
    } catch (error) {
      console.error('Service error - getProviderModel:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Tạo admin provider model mới
   */
  static async createProviderModel(
    data: CreateProviderModelDto
  ): Promise<ProviderModel> {
    try {
      // Validate required fields
      if (!data.name || data.name.trim() === '') {
        throw new Error('Provider model name is required');
      }

      if (!data.type) {
        throw new Error('Provider type is required');
      }

      if (!data.apiKey || data.apiKey.trim() === '') {
        throw new Error('API key is required');
      }

      // Sanitize data
      const sanitizedData: CreateProviderModelDto = {
        name: data.name.trim(),
        type: data.type,
        apiKey: data.apiKey.trim(),
      };

      const response = await apiClient.post<ProviderModel>(
        API_BASE_URL,
        sanitizedData
      );

      return response.result;
    } catch (error) {
      console.error('Service error - createProviderModel:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Cập nhật admin provider model
   */
  static async updateProviderModel(
    id: string,
    data: UpdateProviderModelDto
  ): Promise<ProviderModel> {
    try {
      if (!id || id.trim() === '') {
        throw new Error('Provider model ID is required');
      }

      // Validate at least one field is provided
      if (!data.name && !data.apiKey) {
        throw new Error('At least one field must be provided for update');
      }

      // Sanitize data
      const sanitizedData: UpdateProviderModelDto = {};
      if (data.name && data.name.trim() !== '') {
        sanitizedData.name = data.name.trim();
      }
      if (data.apiKey && data.apiKey.trim() !== '') {
        sanitizedData.apiKey = data.apiKey.trim();
      }

      const response = await apiClient.put<ProviderModel>(
        `${API_BASE_URL}/${id.trim()}`,
        sanitizedData
      );

      return response.result;
    } catch (error) {
      console.error('Service error - updateProviderModel:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Xóa admin provider model
   */
  static async deleteProviderModel(
    id: string
  ): Promise<void> {
    try {
      if (!id || id.trim() === '') {
        throw new Error('Provider model ID is required');
      }

      const response = await apiClient.delete<void>(
        `${API_BASE_URL}/${id.trim()}`
      );

      return response.result;
    } catch (error) {
      console.error('Service error - deleteProviderModel:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Xử lý lỗi chung
   */
  private static handleError(error: unknown): Error {
    if (error instanceof Error) {
      return error;
    }

    if (typeof error === 'object' && error !== null) {
      const errorObj = error as Record<string, unknown>;

      // Handle API response errors
      if (errorObj.response && typeof errorObj.response === 'object') {
        const response = errorObj.response as Record<string, unknown>;
        if (response.data && typeof response.data === 'object') {
          const data = response.data as Record<string, unknown>;
          if (typeof data.message === 'string') {
            return new Error(data.message);
          }
        }
      }

      // Handle network errors
      if (typeof errorObj.message === 'string') {
        return new Error(errorObj.message);
      }
    }

    return new Error('An unknown error occurred');
  }

  /**
   * Xóa nhiều provider models
   */
  static async deleteMultipleProviderModels(ids: string[]): Promise<ApiResponseDto<{ message: string }>> {
    try {
      if (!ids || ids.length === 0) {
        throw new Error('At least one provider model ID is required');
      }

      // Validate and sanitize IDs
      const validIds = ids
        .map(id => id.trim())
        .filter(id => id !== '');

      if (validIds.length === 0) {
        throw new Error('No valid provider model IDs provided');
      }

      return await apiClient.delete(`${API_BASE_URL}/bulk`, { data: { ids: validIds } });
    } catch (error) {
      console.error('Service error - deleteMultipleProviderModels:', error);
      throw this.handleError(error);
    }
  }
}

// Export API functions for direct use
export const {
  getProviderModels,
  getProviderModel,
  createProviderModel,
  updateProviderModel,
  deleteProviderModel,
} = AdminProviderModelService;
