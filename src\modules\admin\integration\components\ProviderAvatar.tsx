import React, { useState } from 'react';
import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';
import { getProviderIcon } from '../provider-model/types';
import Icon from '@/shared/components/common/Icon';

interface ProviderAvatarProps {
  /**
   * Provider type
   */
  type: TypeProviderEnum;

  /**
   * Avatar URL (có thể null)
   */
  avatar?: string | null;

  /**
   * Kích thước avatar
   */
  size?: 'sm' | 'md' | 'lg' | 'xl';

  /**
   * CSS classes bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị avatar của provider với fallback
 */
const ProviderAvatar: React.FC<ProviderAvatarProps> = ({
  type,
  avatar,
  size = 'md',
  className = ''
}) => {
  const [imageError, setImageError] = useState(false);

  // Xác đị<PERSON> kích thước
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-20 h-20'
  };

  const iconSizes = {
    sm: 'sm' as const,
    md: 'lg' as const,
    lg: 'xl' as const,
    xl: 'xl' as const
  };

  // Nếu có avatar và không bị lỗi, hiển thị avatar
  if (avatar && !imageError) {
    return (
      <div className={`${sizeClasses[size]} rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800 ${className}`}>
        <img
          src={avatar}
          alt="Provider Avatar"
          className="w-full h-full object-cover"
          onError={() => setImageError(true)}
        />
      </div>
    );
  }

  // Fallback: hiển thị icon với background
  return (
    <div className={`${sizeClasses[size]} rounded-lg bg-primary/10 dark:bg-primary/20 flex items-center justify-center ${className}`}>
      <Icon
        name={getProviderIcon(type)}
        size={iconSizes[size]}
        className="text-primary dark:text-primary-400"
      />
    </div>
  );
};

export default ProviderAvatar;
