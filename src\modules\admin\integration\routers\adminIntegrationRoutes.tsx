import { Loading } from '@/shared/components';
import AdminLayout from '@/shared/layouts/AdminLayout';
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const AdminIntegrationManagementPage = lazy(() => import('../pages/AdminIntegrationManagementPage'));
const EmailServerManagementPage = lazy(() => import('../pages/EmailServerManagementPage'));
const AdminProviderModelManagementPage = lazy(() => import('../pages/AdminProviderModelManagementPage'));

const adminIntegrationRoutes: RouteObject[] = [
  {
    path: '/admin/integration',
    element: (
      <AdminLayout title="Admin Integration Management">
        <Suspense fallback={<Loading />}>
          <AdminIntegrationManagementPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/integration/email',
    element: (
      <AdminLayout title="Admin Email Server Management">
        <Suspense fallback={<Loading />}>
          <EmailServerManagementPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/integration/provider-model',
    element: (
      <AdminLayout title="Admin Provider Model Management">
        <Suspense fallback={<Loading />}>
          <AdminProviderModelManagementPage />
        </Suspense>
      </AdminLayout>
    ),
  },
];

export default adminIntegrationRoutes;
