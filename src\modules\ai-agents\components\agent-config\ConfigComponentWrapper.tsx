/**
 * Wrapper component cho các config components để implement accordion behavior
 */

import React, { ReactNode } from 'react';
import { CollapsibleCard } from '@/shared/components/common';
import { useAgentConfigAccordion } from '../../hooks/useAgentConfigAccordion';
import { ConfigComponentId } from '../../contexts/AgentConfigAccordionContext';

interface ConfigComponentWrapperProps {
  componentId: ConfigComponentId;
  title: ReactNode;
  children: ReactNode;
  className?: string;
}

/**
 * Wrapper component tự động tích hợp với AgentConfigAccordionContext
 */
export const ConfigComponentWrapper: React.FC<ConfigComponentWrapperProps> = ({
  componentId,
  title,
  children,
  className = "mb-6",
}) => {
  const { isComponentOpen, toggleComponent } = useAgentConfigAccordion();

  const isOpen = isComponentOpen(componentId);

  const handleToggle = (newIsOpen: boolean) => {
    if (newIsOpen) {
      toggleComponent(componentId);
    } else {
      toggleComponent(componentId); // Sẽ đóng component hiện tại
    }
  };

  return (
    <CollapsibleCard
      title={title}
      className={className}
      isOpen={isOpen}
      onToggle={handleToggle}
      lazyLoad={true}
    >
      {children}
    </CollapsibleCard>
  );
};
