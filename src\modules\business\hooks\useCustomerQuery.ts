import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { useCorsAwareFileUpload } from '@/shared/hooks/common';
import {
  CustomerService,
  CreateConvertCustomerDto,
  UpdateCustomerBasicInfoDto,
  UpdateCustomerCustomFieldsDto,
  UpdateCustomerSocialLinksDto
} from '../services/customer.service';
import {
  QueryUserConvertCustomerDto,
  UserConvertCustomerListItemDto,
} from '../types/customer.types';
import { NotificationUtil } from '@/shared/utils/notification';
import { AxiosError } from 'axios';

/**
 * Query keys cho customer API
 */
export const CUSTOMER_QUERY_KEYS = {
  all: ['business', 'customers'] as const,
  list: (params: QueryUserConvertCustomerDto) => [...CUSTOMER_QUERY_KEYS.all, 'list', params] as const,
  detail: (id: number) => [...CUSTOMER_QUERY_KEYS.all, 'detail', id] as const,
};

/**
 * Hook lấy danh sách khách hàng chuyển đổi
 */
export const useConvertCustomers = (params: QueryUserConvertCustomerDto = {}) => {
  return useQuery({
    queryKey: CUSTOMER_QUERY_KEYS.list(params),
    queryFn: () => CustomerService.getConvertCustomers(params),
    select: (data) => data.result,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook lấy chi tiết khách hàng chuyển đổi
 */
export const useConvertCustomer = (id?: number) => {
  return useQuery({
    queryKey: CUSTOMER_QUERY_KEYS.detail(id || 0),
    queryFn: () => CustomerService.getConvertCustomerById(id || 0),
    enabled: !!id, // Chỉ gọi API khi có ID
    select: (data) => data.result,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook tạo khách hàng chuyển đổi mới
 */
export const useCreateConvertCustomer = (options?: {
  showSuccessNotification?: boolean;
  showErrorNotification?: boolean;
}) => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();
  const { showSuccessNotification = true, showErrorNotification = false } = options || {};

  return useMutation({
    mutationFn: (data: CreateConvertCustomerDto) =>
      CustomerService.createConvertCustomer(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách khách hàng
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.all });

      if (showSuccessNotification) {
        NotificationUtil.success({ message: t('business:customer.messages.createSuccess') || 'Tạo khách hàng thành công' });
      }
    },
    onError: (error: AxiosError) => {
      console.error('Lỗi khi tạo khách hàng:', error);

      if (showErrorNotification) {
        NotificationUtil.error({
          message: t('business:customer.messages.createError') || 'Có lỗi xảy ra khi tạo khách hàng'
        });
      }

      // Không re-throw error, để parent component xử lý thông qua mutateAsync catch
    },
  });
};

/**
 * Hook cập nhật khách hàng chuyển đổi
 */
export const useUpdateConvertCustomer = () => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<UserConvertCustomerListItemDto> }) =>
      CustomerService.updateConvertCustomer(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch danh sách khách hàng
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.all });
      // Invalidate chi tiết khách hàng cụ thể
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.detail(variables.id) });

      NotificationUtil.success({
        message: t('business:customer.messages.updateSuccess') || 'Cập nhật khách hàng thành công'
      });
    },
    onError: (error: AxiosError) => {
      console.error('Lỗi khi cập nhật khách hàng:', error);
      NotificationUtil.error({
        message: t('business:customer.messages.updateError') || 'Có lỗi xảy ra khi cập nhật khách hàng'
      });
    },
  });
};

/**
 * Hook xóa khách hàng chuyển đổi
 */
export const useDeleteConvertCustomer = () => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => CustomerService.deleteConvertCustomer(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách khách hàng
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.all });

      NotificationUtil.success({
        message: t('business:customer.messages.deleteSuccess') || 'Xóa khách hàng thành công'
      });
    },
    onError: (error: AxiosError) => {
      console.error('Lỗi khi xóa khách hàng:', error);
      NotificationUtil.error({
        message: t('business:customer.messages.deleteError') || 'Có lỗi xảy ra khi xóa khách hàng'
      });
    },
  });
};

/**
 * Hook xóa nhiều khách hàng chuyển đổi cùng lúc
 */
export const useBulkDeleteConvertCustomers = () => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (customerIds: number[]) => CustomerService.deleteBulkConvertCustomers(customerIds),
    onSuccess: (_, customerIds) => {
      // Invalidate và refetch danh sách khách hàng
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.all });

      NotificationUtil.success({
        message: t('business:customer.messages.bulkDeleteSuccess', { count: customerIds.length }) ||
                `Xóa thành công ${customerIds.length} khách hàng`
      });
    },
    onError: (error: AxiosError) => {
      console.error('Lỗi khi xóa nhiều khách hàng:', error);
      NotificationUtil.error({
        message: t('business:customer.messages.bulkDeleteError') || 'Có lỗi xảy ra khi xóa khách hàng'
      });
    },
  });
};

/**
 * Hook cập nhật thông tin cơ bản khách hàng với upload avatar
 */
export const useUpdateCustomerBasicInfo = (options?: {
  showSuccessNotification?: boolean;
  showErrorNotification?: boolean;
}) => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  // Sử dụng useCorsAwareFileUpload như MediaPage
  const fileUploadWithQueue = useCorsAwareFileUpload({
    defaultTaskTitle: 'Upload avatar',
    autoAddToQueue: true,
  });

  const {
    showSuccessNotification = true,
    showErrorNotification = true
  } = options || {};

  return useMutation({
    mutationFn: async ({
      id,
      data,
      avatarFile
    }: {
      id: number;
      data: UpdateCustomerBasicInfoDto;
      avatarFile?: File
    }) => {
      // Bước 1: Gọi API cập nhật thông tin cơ bản
      const response = await CustomerService.updateCustomerBasicInfo(id, data);

      // Bước 2: Nếu có avatar file và backend trả về uploadUrl, upload file với queue
      if (avatarFile && response.result.avatarUpload?.uploadUrl) {
        try {
          // Upload file lên presigned URL với TaskQueue như MediaPage
          await fileUploadWithQueue.uploadToUrlWithQueue({
            file: avatarFile,
            presignedUrl: response.result.avatarUpload.uploadUrl,
            taskTitle: `Upload avatar: ${avatarFile.name}`,
            taskDescription: `Kích thước: ${(avatarFile.size / 1024).toFixed(1)} KB`,
            onUploadProgress: (progress) => {
              console.log(`Avatar upload progress: ${progress}%`);
            },
          });
          console.log('Avatar uploaded successfully to queue');
        } catch (uploadError) {
          console.error('Error uploading avatar:', uploadError);
          // Không throw error để không làm fail toàn bộ mutation
          // Queue sẽ xử lý retry và error handling
        }
      }

      return response;
    },
    onSuccess: (_, variables) => {
      // Invalidate và refetch danh sách khách hàng
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.all });
      // Invalidate chi tiết khách hàng cụ thể
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.detail(variables.id) });

      if (showSuccessNotification) {
        NotificationUtil.success({
          message: t('business:customer.messages.updateSuccess') || 'Cập nhật thông tin khách hàng thành công'
        });
      }
    },
    onError: (error: AxiosError) => {
      if (showErrorNotification) {
        NotificationUtil.error({
          message: t('business:customer.messages.updateError') || 'Có lỗi xảy ra khi cập nhật khách hàng'
        });
      }
      console.error('Error updating customer basic info:', error);
    },
  });
};

/**
 * Hook cập nhật custom fields khách hàng
 */
export const useUpdateCustomerCustomFields = (options?: {
  showSuccessNotification?: boolean;
  showErrorNotification?: boolean;
}) => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();
  const { showSuccessNotification = true, showErrorNotification = true } = options || {};

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateCustomerCustomFieldsDto }) =>
      CustomerService.updateCustomerCustomFields(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch danh sách khách hàng
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.all });
      // Invalidate chi tiết khách hàng cụ thể
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.detail(variables.id) });

      if (showSuccessNotification) {
        NotificationUtil.success({
          message: t('business:customer.messages.updateSuccess') || 'Cập nhật trường tùy chỉnh thành công'
        });
      }
    },
    onError: (error: AxiosError) => {
      if (showErrorNotification) {
        NotificationUtil.error({
          message: t('business:customer.messages.updateError') || 'Có lỗi xảy ra khi cập nhật trường tùy chỉnh'
        });
      }
      console.error('Error updating customer custom fields:', error);
    },
  });
};

/**
 * Hook cập nhật social links khách hàng
 */
export const useUpdateCustomerSocialLinks = (options?: {
  showSuccessNotification?: boolean;
  showErrorNotification?: boolean;
}) => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();
  const { showSuccessNotification = true, showErrorNotification = true } = options || {};

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateCustomerSocialLinksDto }) =>
      CustomerService.updateCustomerSocialLinks(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch danh sách khách hàng
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.all });
      // Invalidate chi tiết khách hàng cụ thể
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.detail(variables.id) });

      if (showSuccessNotification) {
        NotificationUtil.success({
          message: t('business:customer.messages.updateSuccess') || 'Cập nhật liên kết mạng xã hội thành công'
        });
      }
    },
    onError: (error: AxiosError) => {
      if (showErrorNotification) {
        NotificationUtil.error({
          message: t('business:customer.messages.updateError') || 'Có lỗi xảy ra khi cập nhật liên kết mạng xã hội'
        });
      }
      console.error('Error updating customer social links:', error);
    },
  });
};

// Alias cho compatibility với OrderForm components
export const useCustomerQuery = useConvertCustomers;
export const useCreateCustomer = useCreateConvertCustomer;
