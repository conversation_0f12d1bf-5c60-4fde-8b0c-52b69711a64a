import { lazy, Suspense } from 'react';
import { RouteObject } from 'react-router-dom';
import MainLayout from '@/shared/layouts/MainLayout';
import { Loading } from '@/shared/components/common';
import i18n from '@/lib/i18n';
import UserIntegrationManagementPage from '../pages/UserIntegrationManagementPage';

const BankAccountIntegrationPage = lazy(() => import('../pages/BankAccountIntegrationPage'));
const FacebookIntegrationPage = lazy(() => import('../pages/FacebookIntegrationPage'));
const WebsiteIntegrationPage = lazy(() => import('../pages/WebsiteIntegrationPage'));
const EmailServerManagementPage = lazy(() => import('../pages/EmailServerManagementPage'));
const ProviderModelManagementPage = lazy(() => import('../pages/ProviderModelManagementPage'));
const SMSIntegrationPage = lazy(() => import('../pages/SMSIntegrationPage'));
const DatabaseIntegrationPage = lazy(() => import('../pages/DatabaseIntegrationPage'));

// New integrations
const GoogleCalendarIntegrationPage = lazy(() => import('../calendar/pages/GoogleCalendarIntegrationPage'));
const ShippingIntegrationPage = lazy(() => import('../pages/ShippingIntegrationPage'));
const CloudStorageIntegrationPage = lazy(() => import('../pages/CloudStorageIntegrationPage'));
const EnterpriseStorageIntegrationPage = lazy(() => import('../pages/EnterpriseStorageIntegrationPage'));
const ExternalAgentIntegrationPage = lazy(() => import('../pages/ExternalAgentIntegrationPage'));
const t = (key: string, defaultValue?: string) => i18n.t(key, { defaultValue });

const integrationRoutes: RouteObject[] = [
  // Trang chính - Hiển thị tất cả các mạng xã hội
  {
    path: '/integrations',
    element: (
      <MainLayout title={t('integration.social.title')}>
        <Suspense fallback={<Loading />}>
          <UserIntegrationManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang quản lý tài khoản đã liên kết
  {
    path: '/integrations/email',
    element: (
      <MainLayout title={t('integration.accounts.title')}>
        <Suspense fallback={<Loading />}>
          <EmailServerManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },
  // Trang quản lý tài khoản đã liên kết
  {
    path: '/integrations/facebook',
    element: (
      <MainLayout title={t('integration.facebook.title')}>
        <Suspense fallback={<Loading />}>
          <FacebookIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/website',
    element: (
      <MainLayout title={t('integration.website.title')}>
        <Suspense fallback={<Loading />}>
          <WebsiteIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  // Trang quản lý tích hợp SMS
  {
    path: '/integrations/sms',
    element: (
      <MainLayout title={t('integration.sms.title')}>
        <Suspense fallback={<Loading />}>
          <SMSIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  // Trang quản lý tích hợp Database
  {
    path: '/integrations/database',
    element: (
      <MainLayout title={t('integration.database.title')}>
        <Suspense fallback={<Loading />}>
          <DatabaseIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/provider-model',
    element: (
      <MainLayout title={t('integration.providerModel.title')}>
        <Suspense fallback={<Loading />}>
          <ProviderModelManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/user/bank-accounts',
    element: (
      <MainLayout title={t('integration.bankAccounts.title')}>
        <Suspense fallback={<Loading />}>
          <BankAccountIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Google Calendar Integration
  {
    path: '/integrations/google-calendar',
    element: (
      <MainLayout title={t('integration.calendar.title')}>
        <Suspense fallback={<Loading />}>
          <GoogleCalendarIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Shipping Integration
  {
    path: '/integrations/shipping',
    element: (
      <MainLayout title={t('integration.shipping.title')}>
        <Suspense fallback={<Loading />}>
          <ShippingIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Cloud Storage Integration
  {
    path: '/integrations/cloud-storage',
    element: (
      <MainLayout title={t('integration.cloudStorage.title')}>
        <Suspense fallback={<Loading />}>
          <CloudStorageIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Enterprise Storage Integration
  {
    path: '/integrations/enterprise-storage',
    element: (
      <MainLayout title={t('integration.enterpriseStorage.title')}>
        <Suspense fallback={<Loading />}>
          <EnterpriseStorageIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // External Agent Integration
  {
    path: '/integrations/external-agents',
    element: (
      <MainLayout title={t('integration.externalAgents.title')}>
        <Suspense fallback={<Loading />}>
          <ExternalAgentIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default integrationRoutes;
