import { apiClient } from '@/shared/api';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';

// Import types từ thư mục types
import {
  GetAgentsQueryDto,
  GetTypeAgentsQueryDto,
  GetBaseModelsQueryDto,
  AgentStatisticsQueryDto,
  CreateAgentDto,
  CreateAgentModularDto,
  UpdateAgentDto,
  CreateAgentResponseDto,
  UpdateAgentResponseDto,
  AgentStatisticsResponseDto,
  UpdateAgentVectorStoreDto,
  AgentListResponse,
  TypeAgentListResponse,
  BaseModelListResponse,
  AgentDetailDto,
  TypeAgentDetailDto,
  BaseModelUserResponseDto,
  UserProviderModelResponseDto
} from '../types';

// Tất cả interfaces đã đượ<PERSON> chuyể<PERSON> sang thư mục types
// File này chỉ chứa các API functions

/**
 * L<PERSON>y danh sách agents
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getAgents = async (
  params?: GetAgentsQueryDto
): Promise<ApiResponse<AgentListResponse>> => {
  return apiClient.get('/user/agents', { params });
};

/**
 * Lấy chi tiết agent theo ID
 * @param id ID của agent
 * @returns Promise với response từ API
 */
export const getAgentDetail = async (
  id: string
): Promise<ApiResponse<AgentDetailDto>> => {
  return apiClient.get(`/user/agents/${id}`);
};

/**
 * Tạo agent mới (Legacy)
 * @param data Dữ liệu tạo agent
 * @returns Promise với response từ API
 */
export const createAgent = async (
  data: CreateAgentDto
): Promise<ApiResponse<CreateAgentResponseDto>> => {
  return apiClient.post('/user/agents', data);
};

/**
 * Tạo agent mới với cấu trúc modular (Recommended)
 * @param data Dữ liệu tạo agent modular
 * @returns Promise với response từ API
 */
export const createAgentModular = async (
  data: CreateAgentModularDto
): Promise<ApiResponse<CreateAgentResponseDto>> => {
  return apiClient.post('/user/agents', data);
};

/**
 * Cập nhật agent
 * @param id ID của agent
 * @param data Dữ liệu cập nhật
 * @returns Promise với response từ API
 */
export const updateAgent = async (
  id: string,
  data: UpdateAgentDto
): Promise<ApiResponse<UpdateAgentResponseDto>> => {
  return apiClient.patch(`/user/agents/${id}`, data);
};

/**
 * Xóa agent
 * @param id ID của agent
 * @returns Promise với response từ API
 */
export const deleteAgent = async (id: string): Promise<ApiResponse<void>> => {
  return apiClient.delete(`/user/agents/${id}`);
};

/**
 * Bật/tắt trạng thái hoạt động của agent
 * @param id ID của agent
 * @returns Promise với response từ API
 */
export const toggleAgentActive = async (
  id: string
): Promise<ApiResponse<{ active: boolean }>> => {
  return apiClient.patch(`/user/agents/${id}/active`);
};

/**
 * Lấy thống kê agent
 * @param id ID của agent
 * @param params Tham số query thống kê
 * @returns Promise với response từ API
 */
export const getAgentStatistics = async (
  id: string,
  params?: AgentStatisticsQueryDto
): Promise<ApiResponse<AgentStatisticsResponseDto>> => {
  return apiClient.get(`/user/agents/${id}/statistics`, { params });
};

/**
 * Cập nhật vector store cho agent
 * @param id ID của agent
 * @param data Dữ liệu vector store
 * @returns Promise với response từ API
 */
export const updateAgentVectorStore = async (
  id: string,
  data: UpdateAgentVectorStoreDto
): Promise<ApiResponse<void>> => {
  return apiClient.patch(`/user/agents/${id}/vector-store`, data);
};

/**
 * Lấy danh sách type agents
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getTypeAgents = async (
  params?: GetTypeAgentsQueryDto
): Promise<ApiResponse<TypeAgentListResponse>> => {
  return apiClient.get('/user/type-agents', { params });
};

/**
 * Lấy chi tiết type agent theo ID
 * @param id ID của type agent
 * @returns Promise với response từ API
 */
export const getTypeAgentDetail = async (
  id: number
): Promise<ApiResponse<TypeAgentDetailDto>> => {
  return apiClient.get(`/user/type-agents/${id}`);
};

// Removed: createTypeAgent, updateTypeAgent, deleteTypeAgent APIs

/**
 * Lấy danh sách base models từ hệ thống RedAI (mặc định)
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getBaseModels = async (
  params?: GetBaseModelsQueryDto
): Promise<ApiResponse<BaseModelListResponse>> => {
  return apiClient.get('/user/base-models', { params });
};

/**
 * Lấy danh sách base models từ provider cụ thể của user
 * @param providerId ID của provider
 * @returns Promise với response từ API
 */
export const getBaseModelsByUserProvider = async (
  providerId: string
): Promise<ApiResponse<BaseModelUserResponseDto[]>> => {
  return apiClient.get(`/user/base-models/provider/${providerId}`);
};

/**
 * Lấy danh sách user providers
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getUserProviders = async (
  params?: QueryDto
): Promise<ApiResponse<PaginatedResult<UserProviderModelResponseDto>>> => {
  return apiClient.get('/user/provider-model', { params });
};


