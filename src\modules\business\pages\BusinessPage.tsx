import React from 'react';
import { useTranslation } from 'react-i18next';
import { ResponsiveGrid } from '@/shared/components/common';
import { ModuleCard } from '@/modules/components/card';

/**
 * Trang tổng quan về module Business
 */
const BusinessPage: React.FC = () => {
  const { t } = useTranslation('business');

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Sản phẩm */}
        <ModuleCard
          title={t('business:product.title')}
          description={t('business:product.description')}
          icon="box"
          linkTo="/business/product"
        />

        {/* Chuyển đổi */}
        <ModuleCard
          title={t('business:conversion.title')}
          description={t('business:conversion.description')}
          icon="refresh-cw"
          linkTo="/business/conversion"
        />

        {/* Đơn hàng */}
        <ModuleCard
          title={t('business:order.title')}
          description={t('business:order.description')}
          icon="shopping-cart"
          linkTo="/business/order"
        />

        {/* Khách hàng */}
        <ModuleCard
          title={t('business:customer.title')}
          description={t('business:customer.description')}
          icon="users"
          linkTo="/business/customer"
        />

        {/* Báo cáo */}
        <ModuleCard
          title={t('business:report.title')}
          description={t('business:report.description')}
          icon="bar-chart"
          linkTo="/business/report"
        />

        {/* Quản lý kho */}
        <ModuleCard
          title={t('business:inventory.title')}
          description={t('business:inventory.description')}
          icon="package"
          linkTo="/business/inventory"
        />

        {/* Trường tùy chỉnh */}
        <ModuleCard
          title={t('business:customField.title')}
          description={t('business:customField.description')}
          icon="database"
          linkTo="/business/custom-field"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default BusinessPage;
