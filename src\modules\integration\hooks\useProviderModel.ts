/**
 * TanStack Query hooks for Provider Model operations
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ProviderModelService } from '../services/provider-model.service';
import {
  CreateProviderModelDto,
  UpdateProviderModelDto,
  ProviderModelQueryDto,
} from '../types/provider-model.types';
import { NotificationUtil } from '@/shared/utils/notification';

// Query keys
export const PROVIDER_MODEL_QUERY_KEYS = {
  all: ['provider-models'] as const,
  lists: () => [...PROVIDER_MODEL_QUERY_KEYS.all, 'list'] as const,
  list: (params: Partial<ProviderModelQueryDto>) => [...PROVIDER_MODEL_QUERY_KEYS.lists(), params] as const,
  details: () => [...PROVIDER_MODEL_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...PROVIDER_MODEL_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách provider models với phân trang
 */
export const useProviderModels = (params: Partial<ProviderModelQueryDto> = {}) => {
  return useQuery({
    queryKey: PROVIDER_MODEL_QUERY_KEYS.list(params),
    queryFn: () => ProviderModelService.getProviderModels(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook để lấy thông tin chi tiết một provider model
 */
export const useProviderModel = (id: string, enabled = true) => {
  return useQuery({
    queryKey: PROVIDER_MODEL_QUERY_KEYS.detail(id),
    queryFn: () => ProviderModelService.getProviderModel(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook để tạo provider model mới
 */
export const useCreateProviderModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProviderModelDto) =>
      ProviderModelService.createProviderModel(data),
    onSuccess: () => {
      // Invalidate and refetch provider models list
      queryClient.invalidateQueries({
        queryKey: PROVIDER_MODEL_QUERY_KEYS.lists()
      });

      // Show success notification
      NotificationUtil.success({
        title: 'Tạo thành công!',
        message: 'Provider model đã được tạo thành công.',
        duration: 3000,
      });
    },
    onError: (error: Error) => {
      console.error('Error creating provider model:', error);

      // Show error notification
      NotificationUtil.error({
        title: 'Tạo thất bại!',
        message: error.message || 'Có lỗi xảy ra khi tạo provider model.',
        duration: 5000,
      });
    },
  });
};

/**
 * Hook để cập nhật provider model
 */
export const useUpdateProviderModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateProviderModelDto }) =>
      ProviderModelService.updateProviderModel(id, data),
    onSuccess: (response, { id }) => {
      // Update the specific provider model in cache
      queryClient.setQueryData(
        PROVIDER_MODEL_QUERY_KEYS.detail(id),
        response
      );

      // Invalidate and refetch provider models list
      queryClient.invalidateQueries({
        queryKey: PROVIDER_MODEL_QUERY_KEYS.lists()
      });

      // Show success notification
      NotificationUtil.success({
        title: 'Cập nhật thành công!',
        message: 'Provider model đã được cập nhật thành công.',
        duration: 3000,
      });
    },
    onError: (error: Error) => {
      console.error('Error updating provider model:', error);

      // Show error notification
      NotificationUtil.error({
        title: 'Cập nhật thất bại!',
        message: error.message || 'Có lỗi xảy ra khi cập nhật provider model.',
        duration: 5000,
      });
    },
  });
};

/**
 * Hook để xóa provider model
 */
export const useDeleteProviderModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ProviderModelService.deleteProviderModel(id),
    onSuccess: (_, id) => {
      // Remove the specific provider model from cache
      queryClient.removeQueries({
        queryKey: PROVIDER_MODEL_QUERY_KEYS.detail(id)
      });

      // Invalidate and refetch provider models list
      queryClient.invalidateQueries({
        queryKey: PROVIDER_MODEL_QUERY_KEYS.lists()
      });

      // Show success notification
      NotificationUtil.success({
        title: 'Xóa thành công!',
        message: 'Provider model đã được xóa thành công.',
        duration: 3000,
      });
    },
    onError: (error: Error) => {
      console.error('Error deleting provider model:', error);

      // Show error notification
      NotificationUtil.error({
        title: 'Xóa thất bại!',
        message: error.message || 'Có lỗi xảy ra khi xóa provider model.',
        duration: 5000,
      });
    },
  });
};

/**
 * Hook để xóa nhiều provider models cùng lúc
 */
export const useDeleteMultipleProviderModels = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) =>
      ProviderModelService.deleteMultipleProviderModels(ids),
    onSuccess: (_, ids) => {
      // Remove the specific provider models from cache
      ids.forEach(id => {
        queryClient.removeQueries({
          queryKey: PROVIDER_MODEL_QUERY_KEYS.detail(id)
        });
      });

      // Invalidate and refetch provider models list
      queryClient.invalidateQueries({
        queryKey: PROVIDER_MODEL_QUERY_KEYS.lists()
      });

      // Show success notification
      NotificationUtil.success({
        title: 'Xóa thành công!',
        message: `${ids.length} provider model đã được xóa thành công.`,
        duration: 3000,
      });
    },
    onError: (error: Error) => {
      console.error('Error deleting multiple provider models:', error);

      // Show error notification
      NotificationUtil.error({
        title: 'Xóa thất bại!',
        message: error.message || 'Có lỗi xảy ra khi xóa provider models.',
        duration: 5000,
      });
    },
  });
};

/**
 * Hook để validate API key
 */
export const useValidateApiKey = () => {
  return useMutation({
    mutationFn: ({ type, apiKey }: { type: string; apiKey: string }) =>
      ProviderModelService.validateApiKey(type, apiKey),
    onError: (error: Error) => {
      console.error('Error validating API key:', error);

      // Show error notification
      NotificationUtil.error({
        title: 'Kiểm tra API key thất bại!',
        message: error.message || 'Có lỗi xảy ra khi kiểm tra API key.',
        duration: 5000,
      });
    },
  });
};
