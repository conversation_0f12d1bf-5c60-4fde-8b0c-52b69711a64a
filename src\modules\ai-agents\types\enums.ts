/**
 * Enums cho AI Agents module
 * File này chứa tất cả các enum definitions
 */

/**
 * Enum cho trạng thái Type Agent
 */
export enum TypeAgentStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

/**
 * Enum cho loại nhà cung cấp model
 */
export enum TypeProviderEnum {
  REDAI = 'REDAI',
  OPENAI = 'OPENAI',
  ANTHROPIC = 'ANTHROPIC',
  GOOGLE = 'GOOGLE',
  META = 'META',
  DEEPSEEK = 'DEEPSEEK',
  XAI = 'XAI',
}

/**
 * Enum định nghĩa các trường có thể sắp xếp cho BaseModel
 */
export enum BaseModelSortByEnum {
  ID = 'id',
  DESCRIPTION = 'description',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}
