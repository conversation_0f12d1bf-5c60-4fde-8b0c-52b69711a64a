import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Card, Table, ActionMenu, ActionMenuItem, ConfirmDeleteModal } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { usePhysicalWarehouses, useDeletePhysicalWarehouse, useDeleteMultiplePhysicalWarehouses, useCreatePhysicalWarehouse, useUpdatePhysicalWarehouse } from '../hooks/usePhysicalWarehouseQuery';
import {
  QueryPhysicalWarehouseDto,
  PhysicalWarehouseListItemDto,
} from '../types/physical-warehouse.types';
import { WarehouseListItemDto } from '../types/warehouse.types';
import CreatePhysicalWarehouseForm from '../components/forms/CreatePhysicalWarehouseForm';
import EditPhysicalWarehouseForm from '../components/forms/EditPhysicalWarehouseForm';
import { CreatePhysicalWarehouseFormValues, UpdatePhysicalWarehouseFormValues } from '../schemas/physical-warehouse.schema';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang quản lý kho vật lý
 */
const InventoryPage: React.FC = () => {
  const { t } = useTranslation(['business', 'common']);
  const navigate = useNavigate();

  // State cho form và modal
  const { isVisible: isFormVisible, showForm, hideForm } = useSlideForm();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [selectedWarehouseId, setSelectedWarehouseId] = useState<number | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // Xử lý xem chi tiết kho
  const handleViewDetail = useCallback(
    (id: number) => {
      if (id && !isNaN(id)) {
        navigate(`/business/inventory/${id}`);
      } else {
        console.error('Invalid warehouse ID:', id);
      }
    },
    [navigate]
  );

  // Xử lý chỉnh sửa
  const handleEdit = useCallback(
    (id: number) => {
      setSelectedWarehouseId(id);
      showForm();
    },
    [setSelectedWarehouseId, showForm]
  );

  // Xử lý hiển thị modal xác nhận xóa
  const handleShowDeleteConfirm = useCallback(
    (id: number) => {
      setSelectedWarehouseId(id);
      setIsDeleteModalOpen(true);
    },
    [setSelectedWarehouseId]
  );

  // Xử lý thêm sản phẩm vào kho
  const handleAddProduct = useCallback(
    (warehouseId: number) => {
      // TODO: Implement add product to warehouse functionality
      console.log('Add product to warehouse:', warehouseId);
    },
    []
  );

  // Định nghĩa cột cho bảng
  const columns: TableColumn<PhysicalWarehouseListItemDto>[] = useMemo(
    () => [
      {
        key: 'name',
        title: t('business:physicalWarehouse.name'),
        dataIndex: 'name',
        sortable: true,
        render: (value: unknown, record: WarehouseListItemDto) => (
          <button
            onClick={() => handleViewDetail(record.warehouseId)}
            className="text-blue-600 hover:text-blue-800 hover:underline font-medium"
          >
            {value as string}
          </button>
        ),
        align: 'left',
      },
      {
        key: 'address',
        title: t('business:physicalWarehouse.address'),
        dataIndex: 'address',
        sortable: true,
      },
      {
        key: 'capacity',
        title: t('business:physicalWarehouse.capacity'),
        dataIndex: 'capacity',
        render: (value: unknown) => {
          if (!value) return '-';
          return `${value}`;
        },
        sortable: true,
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '15%',
        render: (_, record) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('business:warehouse.viewDetail'),
              icon: 'eye',
              onClick: () => record.warehouseId && handleViewDetail(record.warehouseId),
            },
            {
              id: 'edit',
              label: t('common:edit'),
              icon: 'edit',
              onClick: () => record.warehouseId && handleEdit(record.warehouseId),
            },
            {
              id: 'addProduct',
              label: t('business:inventory.addProduct'),
              icon: 'plus',
              onClick: () => record.warehouseId && handleAddProduct(record.warehouseId),
            },
            {
              id: 'delete',
              label: t('common:delete'),
              icon: 'trash',
              onClick: () => record.warehouseId && handleShowDeleteConfirm(record.warehouseId),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handleViewDetail, handleEdit, handleShowDeleteConfirm, handleAddProduct]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): QueryPhysicalWarehouseDto => {
    const queryParams: QueryPhysicalWarehouseDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<PhysicalWarehouseListItemDto, QueryPhysicalWarehouseDto>({
      columns,
      filterOptions: [
        { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      ],
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách kho vật lý
  const { data: warehousesData, isLoading } = usePhysicalWarehouses(dataTable.queryParams);

  // Xử lý dữ liệu trước khi hiển thị
  const processedData = useMemo(() => {
    if (!warehousesData?.result?.items) return [];

    return warehousesData.result.items.map(item => ({
      ...item,
    }));
  }, [warehousesData]);

  // Mutation để xóa kho vật lý
  const { mutateAsync: deletePhysicalWarehouse } = useDeletePhysicalWarehouse();

  // Mutation để xóa nhiều kho vật lý
  const { mutateAsync: deleteMultiplePhysicalWarehouses } = useDeleteMultiplePhysicalWarehouses();

  // Mutation để tạo kho vật lý mới
  const { mutateAsync: createPhysicalWarehouse } = useCreatePhysicalWarehouse();

  // Mutation để cập nhật kho vật lý
  const { mutateAsync: updatePhysicalWarehouse } = useUpdatePhysicalWarehouse();

  // Wrapper cho hàm handleSortChange để đảm bảo kiểu dữ liệu đúng
  const handleSortChangeWrapper = useCallback(
    (column: string | null, order: SortOrder | null) => {
      // Nếu column hoặc order là null, reset sort
      if (column === null || order === null) {
        dataTable.tableData.handleSortChange(null, null);
        return;
      }

      dataTable.tableData.handleSortChange(column, order as SortOrder);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {},
    t,
  });

  // Xử lý thêm mới
  const handleAdd = () => {
    showForm();
  };

  // Xử lý hủy xóa
  const handleCancelDelete = () => {
    setIsDeleteModalOpen(false);
    setSelectedWarehouseId(null);
  };

  // Xử lý xác nhận xóa
  const handleConfirmDelete = async () => {
    if (selectedWarehouseId) {
      try {
        await deletePhysicalWarehouse(selectedWarehouseId);
        setIsDeleteModalOpen(false);
        setSelectedWarehouseId(null);
        NotificationUtil.success({
          message: t('business:physicalWarehouse.deleteSuccess'),
          duration: 3000,
        });
      } catch (error) {
        console.error('Error deleting physical warehouse:', error);
        NotificationUtil.error({
          message: t('business:physicalWarehouse.deleteError'),
          duration: 3000,
        });
      }
    }
  };

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      NotificationUtil.warning({
        message: t('business:physicalWarehouse.selectToDelete'),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      // Gọi API xóa nhiều kho vật lý cùng lúc
      await deleteMultiplePhysicalWarehouses(selectedRowKeys as number[]);

      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:physicalWarehouse.bulkDeleteSuccess', { count: selectedRowKeys.length }),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting physical warehouses:', error);
      NotificationUtil.error({
        message: t('business:physicalWarehouse.bulkDeleteError'),
        duration: 3000,
      });
    }
  }, [selectedRowKeys, deleteMultiplePhysicalWarehouses, t]);

  // Xử lý submit form tạo mới
  const handleCreateSubmit = async (values: CreatePhysicalWarehouseFormValues) => {
    try {
      await createPhysicalWarehouse(values);
      hideForm();
      NotificationUtil.success({
        message: t('business:physicalWarehouse.createSuccess'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error creating physical warehouse:', error);
      NotificationUtil.error({
        message: t('business:physicalWarehouse.createError'),
        duration: 3000,
      });
    }
  };

  // Xử lý submit form chỉnh sửa
  const handleEditSubmit = async (values: UpdatePhysicalWarehouseFormValues) => {
    if (!selectedWarehouseId) return;

    try {
      await updatePhysicalWarehouse({ id: selectedWarehouseId, data: values });
      hideForm();
      setSelectedWarehouseId(null);
      NotificationUtil.success({
        message: t('business:physicalWarehouse.updateSuccess'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error updating physical warehouse:', error);
      NotificationUtil.error({
        message: t('business:physicalWarehouse.updateError'),
        duration: 3000,
      });
    }
  };

  // Xử lý hủy form
  const handleCancel = () => {
    setSelectedWarehouseId(null);
    hideForm();
  };

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete'),
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Hiển thị ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form container với animation */}
      <SlideInForm isVisible={isFormVisible}>
        {selectedWarehouseId ? (
          <EditPhysicalWarehouseForm
            warehouseId={selectedWarehouseId}
            onSubmit={handleEditSubmit}
            onCancel={handleCancel}
          />
        ) : (
          <CreatePhysicalWarehouseForm
            onSubmit={handleCreateSubmit}
            onCancel={handleCancel}
          />
        )}
      </SlideInForm>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common:confirmDelete')}
        message={t('business:physicalWarehouse.confirmDeleteMessage')}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete')}
        message={t('business:physicalWarehouse.confirmBulkDeleteMessage', { count: selectedRowKeys.length })}
      />

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={processedData}
          rowKey="warehouseId"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: warehousesData?.result.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: warehousesData?.result.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default InventoryPage;
