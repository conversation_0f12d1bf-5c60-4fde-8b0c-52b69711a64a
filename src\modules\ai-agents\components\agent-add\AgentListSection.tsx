import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  Icon,
  Typography,
  Loading,
  EmptyState,
  Avatar,
  Chip,
  Table,
} from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import { AgentListItemDto } from '../../types';
import { formatDate } from '@/shared/utils/date';

interface AgentListSectionProps {
  /**
   * Danh sách agents
   */
  agents: AgentListItemDto[];

  /**
   * Metadata của danh sách agents
   */
  meta?: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
    hasItems: boolean;
  };

  /**
   * Trạng thái loading
   */
  isLoading: boolean;

  /**
   * Lỗi nếu có
   */
  error?: Error | null;

  /**
   * Tên của type agent (để hiển thị trong empty state)
   */
  typeAgentName: string;
}

/**
 * Component hiển thị danh sách agents thuộc một type agent
 */
const AgentListSection: React.FC<AgentListSectionProps> = ({
  agents,
  meta,
  isLoading,
  error,
  typeAgentName,
}) => {
  // const { t } = useTranslation(); // Removed unused variable
  const navigate = useNavigate();

  // Xử lý khi click vào agent để xem chi tiết
  const handleViewAgent = (agentId: string) => {
    navigate(`/ai-agents/${agentId}`);
  };

  // Định nghĩa columns cho table
  const columns: TableColumn<AgentListItemDto>[] = [
    {
      key: 'avatar',
      title: '',
      width: 60,
      render: (_, agent) => (
        <Avatar
          src={agent.avatar || undefined}
          alt={agent.name}
          size="sm"
        />
      ),
    },
    {
      key: 'name',
      title: 'Tên Agent',
      sortable: true,
      render: (_, agent) => (
        <div>
          <div className="font-medium text-gray-900 dark:text-gray-100">
            {agent.name}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            ID: {agent.id}
          </div>
        </div>
      ),
    },
    {
      key: 'level',
      title: 'Level',
      width: 100,
      render: (_, agent) => (
        <div className="text-center">
          <div className="text-lg font-bold text-primary">
            {agent.level}
          </div>
          <div className="text-xs text-gray-500">
            {agent.exp}/{agent.expMax} EXP
          </div>
        </div>
      ),
    },
    {
      key: 'active',
      title: 'Trạng thái',
      width: 100,
      render: (_, agent) => (
        <Chip
          variant={agent.active ? 'success' : 'warning'}
          size="sm"
        >
          {agent.active ? 'Hoạt động' : 'Tạm dừng'}
        </Chip>
      ),
    },
    {
      key: 'model_id',
      title: 'Model',
      width: 120,
      render: (_, agent) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900 dark:text-gray-100">
            {agent.model_id}
          </div>
        </div>
      ),
    },
    {
      key: 'createdAt',
      title: 'Ngày tạo',
      width: 120,
      sortable: true,
      render: (_, agent) => (
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {formatDate(agent.createdAt)}
        </div>
      ),
    },
    {
      key: 'actions',
      title: 'Thao tác',
      width: 100,
      render: (_, agent) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewAgent(agent.id)}
            leftIcon={<Icon name="eye" size="sm" />}
          >
            Xem
          </Button>
        </div>
      ),
    },
  ];

  // Render loading state
  if (isLoading) {
    return (
      <Card>
        <div className="flex justify-center items-center py-12">
          <Loading size="lg" />
        </div>
      </Card>
    );
  }

  // Render error state
  if (error) {
    return (
      <Card>
        <EmptyState
          icon="alert-circle"
          title="Không thể tải danh sách agents"
          description="Có lỗi xảy ra khi tải danh sách agents. Vui lòng thử lại sau."
          className="py-8"
        />
      </Card>
    );
  }

  // Render empty state
  if (!agents || agents.length === 0) {
    return (
      <Card>
        <EmptyState
          icon="users"
          title="Chưa có agents"
          description={`Chưa có agent nào được tạo từ type agent "${typeAgentName}".`}
          actions={
            <Button
              variant="primary"
              onClick={() => navigate('/ai-agents/create')}
              leftIcon={<Icon name="plus" size="sm" />}
            >
              Tạo Agent mới
            </Button>
          }
          className="py-8"
        />
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header với thống kê */}
      <Card variant="bordered">
        <div className="flex items-center justify-between">
          <div>
            <Typography variant="h6" className="text-gray-900 dark:text-gray-100">
              Danh sách Agents
            </Typography>
            <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
              {meta?.totalItems || 0} agents được tạo từ type agent "{typeAgentName}"
            </Typography>
          </div>

          <Button
            variant="primary"
            onClick={() => navigate('/ai-agents/create')}
            leftIcon={<Icon name="plus" size="sm" />}
          >
            Tạo Agent mới
          </Button>
        </div>
      </Card>

      {/* Bảng danh sách agents */}
      <Card>
        <Table
          columns={columns}
          data={agents}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          pagination={false} // Tạm thời không có pagination trong detail view
          className="min-h-[400px]"
        />
      </Card>

      {/* Thống kê nhanh */}
      {meta && (
        <Card variant="bordered">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3">
              <div className="text-2xl font-bold text-primary mb-1">
                {meta.totalItems}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Tổng số agents
              </div>
            </div>

            <div className="text-center p-3">
              <div className="text-2xl font-bold text-green-600 mb-1">
                {agents.filter(agent => agent.active).length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Đang hoạt động
              </div>
            </div>

            <div className="text-center p-3">
              <div className="text-2xl font-bold text-yellow-600 mb-1">
                {agents.filter(agent => !agent.active).length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Tạm dừng
              </div>
            </div>

            <div className="text-center p-3">
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {Math.round(agents.reduce((sum, agent) => sum + agent.level, 0) / agents.length) || 0}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Level trung bình
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default AgentListSection;
