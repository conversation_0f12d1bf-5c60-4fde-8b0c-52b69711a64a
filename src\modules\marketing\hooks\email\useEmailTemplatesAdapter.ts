/**
 * Email Templates Adapter Hooks - Bridge between backend template-email API and frontend expectations
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { EmailTemplateAdapterService } from '../../services/email-template-adapter.service';
import { NotificationUtil } from '@/shared/utils/notification';
import { EmailTemplateQueryDto, CreateEmailTemplateDto, UpdateEmailTemplateDto } from '../../types/email.types';

/**
 * Query keys for Email templates using adapter
 */
export const EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS = {
  all: ['email', 'templates', 'adapter'] as const,
  lists: () => [...EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.all, 'list'] as const,
  list: (query: EmailTemplateQueryDto) => {
    // Tạo stable query key bằng cách chỉ include các giá trị có thực
    const stableQuery: Record<string, string | number> = {};

    if (query.page && query.page !== 1) stableQuery.page = query.page;
    if (query.limit && query.limit !== 10) stableQuery.limit = query.limit;
    if (query.search) stableQuery.search = query.search;
    if (query.sortBy) stableQuery.sortBy = query.sortBy;
    if (query.sortDirection) stableQuery.sortDirection = query.sortDirection;
    if (query.status) stableQuery.status = query.status;

    return [...EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.lists(), stableQuery] as const;
  },
  details: () => [...EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.details(), id] as const,
  statistics: () => [...EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.all, 'statistics'] as const,
  overview: () => [...EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.all, 'overview'] as const,
};

/**
 * Hook to get email templates using adapter
 */
export function useEmailTemplatesAdapter(query?: EmailTemplateQueryDto) {
  const queryKey = EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.list(query || {});

  console.log('🔍 [useEmailTemplatesAdapter] Called with query:', query);
  console.log('🔍 [useEmailTemplatesAdapter] Generated queryKey:', JSON.stringify(queryKey));

  return useQuery({
    queryKey,
    queryFn: () => {
      console.log('🚀 [API CALL] EmailTemplateAdapterService.getEmailTemplates');
      return EmailTemplateAdapterService.getEmailTemplates(query);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

/**
 * Hook to get email template by ID using adapter
 */
export function useEmailTemplateAdapter(id: string) {
  return useQuery({
    queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.detail(id),
    queryFn: () => EmailTemplateAdapterService.getEmailTemplate(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
}

/**
 * Hook to create email template using adapter
 */
export function useCreateEmailTemplateAdapter() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateEmailTemplateDto) => {
      console.log('🚀 [useCreateEmailTemplateAdapter] Creating template with data:', data);

      return EmailTemplateAdapterService.createEmailTemplate({
        name: data.name,
        subject: data.subject,
        htmlContent: data.htmlContent,
        textContent: data.textContent,
        type: data.type,
        previewText: data.previewText,
        tags: data.tags,
        variables: data.variables,
      });
    },
    onSuccess: (result) => {
      console.log('✅ [useCreateEmailTemplateAdapter] Template created successfully:', result);

      // Invalidate templates list
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.statistics() });

      NotificationUtil.success({
        message: 'Tạo template thành công!',
        title: `Template "${result.name}" đã được tạo`,
      });
    },
    onError: (error: Error) => {
      console.error('❌ [useCreateEmailTemplateAdapter] Failed to create template:', error);

      NotificationUtil.error({
        message: 'Tạo template thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to update email template using adapter
 */
export function useUpdateEmailTemplateAdapter() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateEmailTemplateDto }) =>
      EmailTemplateAdapterService.updateEmailTemplate(id, {
        name: data.name,
        subject: data.subject,
        htmlContent: data.htmlContent,
        tags: data.tags,
        variables: data.variables,
      }),
    onSuccess: (result, { id }) => {
      // Update cache for template detail
      queryClient.setQueryData(EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.detail(id), result);

      // Invalidate templates list
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.statistics() });

      NotificationUtil.success({
        message: 'Cập nhật template thành công!',
        title: `Template "${result.name}" đã được cập nhật`,
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Cập nhật template thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to delete email template using adapter
 */
export function useDeleteEmailTemplateAdapter() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => EmailTemplateAdapterService.deleteEmailTemplate(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.detail(id) });

      // Invalidate templates list
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.statistics() });

      NotificationUtil.success({
        message: 'Xóa template thành công!',
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Xóa template thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to get email template by ID using adapter
 */
export function useEmailTemplateAdapter(id: string) {
  return useQuery({
    queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.detail(id),
    queryFn: () => EmailTemplateAdapterService.getEmailTemplate(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

/**
 * Hook to get email template statistics using adapter
 */
export function useEmailTemplateStatisticsAdapter() {
  return useQuery({
    queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.statistics(),
    queryFn: () => EmailTemplateAdapterService.getEmailTemplateStatistics(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
}

/**
 * Hook to get email template overview using adapter
 */
export function useEmailTemplateOverviewAdapter() {
  return useQuery({
    queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.overview(),
    queryFn: () => EmailTemplateAdapterService.getOverview(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

/**
 * Hook to bulk delete email templates using adapter
 */
export function useBulkDeleteEmailTemplatesAdapter() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => EmailTemplateAdapterService.bulkDeleteEmailTemplates(ids),
    onSuccess: (_, ids) => {
      // Invalidate templates list
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS.statistics() });

      NotificationUtil.success({
        message: `Xóa ${ids.length} template thành công!`,
      });
    },
    onError: (error: Error) => {
      console.error('❌ [useBulkDeleteEmailTemplatesAdapter] Error:', error);
      NotificationUtil.error({
        message: 'Xóa template thất bại!',
        title: error.message,
      });
    },
  });
}

/**
 * Hook for email template management using adapter - combines all operations
 */
export function useEmailTemplateManagementAdapter() {
  const createTemplate = useCreateEmailTemplateAdapter();
  const updateTemplate = useUpdateEmailTemplateAdapter();
  const deleteTemplate = useDeleteEmailTemplateAdapter();
  const bulkDeleteTemplates = useBulkDeleteEmailTemplatesAdapter();

  return {
    createTemplate,
    updateTemplate,
    deleteTemplate,
    bulkDeleteTemplates,
    isLoading: createTemplate.isPending || updateTemplate.isPending || deleteTemplate.isPending || bulkDeleteTemplates.isPending,
  };
}
