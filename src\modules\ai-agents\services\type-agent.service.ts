import {
  getTypeAgents,
  getTypeAgentDetail,
} from '../api/agent.api';

import {
  GetTypeAgentsQueryDto,
  TypeAgentListResponse,
  TypeAgentDetailDto,
  SortDirection,
  TypeAgentSortBy,
} from '../types';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';

/**
 * Service layer cho Type Agent - chứa business logic
 * Theo pattern của blog module
 */

/**
 * Lấy danh sách type agents với business logic
 * @param params Query params
 * @returns Promise với response từ API
 */
export const getTypeAgentsWithBusinessLogic = async (
  params?: GetTypeAgentsQueryDto
): Promise<ApiResponse<TypeAgentListResponse>> => {
  // Business logic có thể bao gồm:
  // - Filter by user permissions
  // - Apply default sorting
  // - Cache frequently accessed data

  // Giữ nguyên params từ frontend, không ghi đè isSystem
  const processedParams = {
    ...params,
    // Không ghi đè isSystem để filter hoạt động đúng
  };

  return getTypeAgents(processedParams);
};

/**
 * Lấy chi tiết type agent với business logic
 * @param id ID của type agent
 * @returns Promise với response từ API
 */
export const getTypeAgentDetailWithBusinessLogic = async (
  id: number
): Promise<ApiResponse<TypeAgentDetailDto>> => {
  // Business logic có thể bao gồm:
  // - Check access permissions
  // - Enrich data with additional info
  // - Track usage analytics

  return getTypeAgentDetail(id);
};

// Removed: createTypeAgentWithBusinessLogic, updateTypeAgentWithBusinessLogic, deleteTypeAgentWithBusinessLogic

/**
 * Lấy type agents cho dropdown selection
 * @returns Promise với danh sách type agents phù hợp cho selection
 */
export const getTypeAgentsForSelection = async (): Promise<ApiResponse<TypeAgentListResponse>> => {
  // Business logic specific cho selection:
  // - Chỉ lấy system type agents
  // - Sắp xếp theo tên
  // - Limit số lượng phù hợp

  const params: GetTypeAgentsQueryDto = {
    isSystem: true,
    limit: 100,
    sortBy: TypeAgentSortBy.NAME,
    sortDirection: SortDirection.ASC,
  };

  return getTypeAgents(params);
};
