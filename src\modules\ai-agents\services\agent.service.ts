import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import {
  createAgent,
  createAgentModular,
  deleteAgent,
  getAgentDetail,
  getAgents,
  getAgentStatistics,
  toggleAgentActive,
  updateAgent,
  updateAgentVectorStore,
} from '../api/agent.api';

import {
  AgentDetailDto,
  AgentListResponse,
  AgentStatisticsQueryDto,
  AgentStatisticsResponseDto,
  CreateAgentDto,
  CreateAgentModularDto,
  CreateAgentResponseDto,
  GetAgentsQueryDto,
  UpdateAgentDto,
  UpdateAgentResponseDto,
  UpdateAgentVectorStoreDto,
} from '../types';

/**
 * Service layer cho Agent - chứa business logic
 * Theo pattern của blog module
 */

/**
 * Lấy danh sách agents với business logic
 * @param params Query params
 * @returns Promise với response từ API
 */
export const getAgentsWithBusinessLogic = async (
  params?: GetAgentsQueryDto
): Promise<ApiResponse<AgentListResponse>> => {
  // Có thể thêm business logic ở đây như:
  // - Validate params
  // - Transform data
  // - Cache logic
  // - Error handling

  return getAgents(params);
};

/**
 * Lấy chi tiết agent với business logic
 * @param id ID của agent
 * @returns Promise với response từ API
 */
export const getAgentDetailWithBusinessLogic = async (
  id: string
): Promise<ApiResponse<AgentDetailDto>> => {
  // Business logic có thể bao gồm:
  // - Validate ID format
  // - Check permissions
  // - Transform response data

  return getAgentDetail(id);
};

/**
 * Tạo agent mới với business logic (Legacy)
 * @param data Dữ liệu tạo agent
 * @returns Promise với response từ API
 */
export const createAgentWithBusinessLogic = async (
  data: CreateAgentDto
): Promise<ApiResponse<CreateAgentResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Validate input data
  // - Transform data format
  // - Set default values
  // - Pre-processing

  // Ví dụ: Set default model config nếu không có
  const processedData = {
    ...data,
    modelConfig: {
      modelId: 'gpt-4o',
      temperature: 0.7,
      top_p: 0.9,
      top_k: 40,
      max_tokens: 1000,
      ...data.modelConfig,
    },
  };

  return createAgent(processedData);
};

/**
 * Tạo agent mới với cấu trúc modular (Recommended)
 * @param data Dữ liệu tạo agent modular
 * @returns Promise với response từ API
 */
export const createAgentModularWithBusinessLogic = async (
  data: CreateAgentModularDto
): Promise<ApiResponse<CreateAgentResponseDto>> => {
  // Business logic cho modular agent:
  // - Validate modular blocks
  // - Set default model config
  // - Validate conditional blocks
  // - Pre-processing

  // Ensure model config có đầy đủ required fields (không bao gồm modelId)
  const defaultModelConfig = {
    temperature: 0.7,
    top_p: 0.9,
    top_k: 40,
    max_tokens: 1000,
  };

  const processedData: CreateAgentModularDto = {
    ...data,
    modelConfig: {
      ...defaultModelConfig,
      ...data.modelConfig, // Override defaults với data từ user
    },
  };

  // Ensure model selection scenario is provided
  if (!data.model_base_id && !data.model_finetuning_id && !(data.model_id && data.provider_id)) {
    // Default to personal model scenario if no model selection provided
    processedData.model_id = 'gpt-4o';
    processedData.provider_id = 'default-provider-id'; // TODO: Get from API
  }

  // Validate blocks dựa trên business rules
  if (processedData.profile && !processedData.profile.gender) {
    // Set default gender nếu không có
    processedData.profile.gender = 'OTHER';
  }

  if (processedData.multiAgent?.subAgentIds) {
    // Validate multi-agent configuration
    // Ensure subAgentIds are valid UUIDs (basic validation)
    processedData.multiAgent.subAgentIds = processedData.multiAgent.subAgentIds.filter(id =>
      typeof id === 'string' && id.length > 0
    );
  }

  return createAgentModular(processedData);
};

/**
 * Cập nhật agent với business logic
 * @param id ID của agent
 * @param data Dữ liệu cập nhật
 * @returns Promise với response từ API
 */
export const updateAgentWithBusinessLogic = async (
  id: string,
  data: UpdateAgentDto
): Promise<ApiResponse<UpdateAgentResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Validate permissions
  // - Merge with existing data
  // - Transform data

  return updateAgent(id, data);
};

/**
 * Xóa agent với business logic
 * @param id ID của agent
 * @returns Promise với response từ API
 */
export const deleteAgentWithBusinessLogic = async (
  id: string
): Promise<ApiResponse<void>> => {
  // Business logic có thể bao gồm:
  // - Check dependencies
  // - Cleanup related data
  // - Audit logging

  return deleteAgent(id);
};

/**
 * Bật/tắt agent với business logic
 * @param id ID của agent
 * @returns Promise với response từ API
 */
export const toggleAgentActiveWithBusinessLogic = async (
  id: string
): Promise<ApiResponse<{ active: boolean }>> => {
  // Business logic có thể bao gồm:
  // - Check business rules
  // - Update related systems
  // - Notifications

  return toggleAgentActive(id);
};

/**
 * Lấy thống kê agent với business logic
 * @param id ID của agent
 * @param params Query params
 * @returns Promise với response từ API
 */
export const getAgentStatisticsWithBusinessLogic = async (
  id: string,
  params?: AgentStatisticsQueryDto
): Promise<ApiResponse<AgentStatisticsResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Calculate additional metrics
  // - Apply filters
  // - Format data for display

  return getAgentStatistics(id, params);
};

/**
 * Cập nhật vector store với business logic
 * @param id ID của agent
 * @param data Dữ liệu vector store
 * @returns Promise với response từ API
 */
export const updateAgentVectorStoreWithBusinessLogic = async (
  id: string,
  data: UpdateAgentVectorStoreDto
): Promise<ApiResponse<void>> => {
  // Business logic có thể bao gồm:
  // - Validate vector store exists
  // - Check compatibility
  // - Update indexes

  return updateAgentVectorStore(id, data);
};
