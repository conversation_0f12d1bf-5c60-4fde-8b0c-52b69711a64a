import { Chip, DatePicker, Input, Select, Icon } from '@/shared/components/common';
import React, { KeyboardEvent, useState } from 'react';
import { ProfileData } from '../../types';
import { ConfigComponentWrapper } from './ConfigComponentWrapper';

interface ProfileConfigProps {
  initialData?: ProfileData;
  onSave?: (data: ProfileData) => void;
}

/**
 * Component cấu hình thông tin profile của Agent
 */
const ProfileConfig: React.FC<ProfileConfigProps> = ({
  initialData,
  onSave
}) => {
  const [profileData, setProfileData] = useState<ProfileData>(initialData || {
    birthDate: '01/01/2000',
    gender: 'Nam',
    language: 'Tiếng Việt',
    education: 'Đại học',
    country: 'Việt Nam',
    position: 'Sales Assistant',
    skills: ['Tư vấn', 'Hỗ trợ khách hàng'],
    personality: 'Th<PERSON> thiện, nhiệt tình, chuy<PERSON>n nghiệp',
  });

  // State cho input kỹ năng và tính cách
  const [skillInput, setSkillInput] = useState('');
  const [personalityInput, setPersonalityInput] = useState('');

  // Xử lý khi thay đổi input text
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Xử lý khi thay đổi select
  const handleSelectChange = (name: string, value: string | number | string[] | number[]) => {
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Xử lý khi thay đổi ngày sinh
  const handleDateChange = (date: Date | null) => {
    if (date) {
      const formattedDate = date.toLocaleDateString('vi-VN');
      setProfileData(prev => ({
        ...prev,
        birthDate: formattedDate
      }));
    }
  };

  // Xử lý khi thay đổi skills trực tiếp từ Select (không sử dụng trong thiết kế mới)

  // Xử lý khi thêm kỹ năng mới
  const handleAddSkill = () => {
    if (skillInput.trim() && !profileData.skills.includes(skillInput.trim())) {
      setProfileData(prev => ({
        ...prev,
        skills: [...prev.skills, skillInput.trim()]
      }));
      setSkillInput('');

      // Gọi callback onSave nếu có
      if (onSave) {
        onSave({
          ...profileData,
          skills: [...profileData.skills, skillInput.trim()]
        });
      }
    }
  };

  // Xử lý khi nhấn Enter trong input kỹ năng
  const handleSkillInputKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddSkill();
    }
  };

  // Xử lý khi xóa kỹ năng
  const handleRemoveSkill = (skill: string) => {
    setProfileData(prev => ({
      ...prev,
      skills: prev.skills.filter(s => s !== skill)
    }));

    // Gọi callback onSave nếu có
    if (onSave) {
      onSave({
        ...profileData,
        skills: profileData.skills.filter(s => s !== skill)
      });
    }
  };

  // Xử lý khi thêm tính cách mới
  const handleAddPersonality = () => {
    if (personalityInput.trim()) {
      const newPersonality = profileData.personality
        ? `${profileData.personality}, ${personalityInput.trim()}`
        : personalityInput.trim();

      setProfileData(prev => ({
        ...prev,
        personality: newPersonality
      }));
      setPersonalityInput('');

      // Gọi callback onSave nếu có
      if (onSave) {
        onSave({
          ...profileData,
          personality: newPersonality
        });
      }
    }
  };

  // Xử lý khi xóa tính cách
  const handleRemovePersonality = (traitToRemove: string) => {
    // Tách chuỗi tính cách thành mảng, lọc bỏ tính cách cần xóa, sau đó nối lại
    const traits = profileData.personality.split(',')
      .map(trait => trait.trim())
      .filter(trait => trait !== traitToRemove);

    const newPersonality = traits.join(', ');

    setProfileData(prev => ({
      ...prev,
      personality: newPersonality
    }));

    // Gọi callback onSave nếu có
    if (onSave) {
      onSave({
        ...profileData,
        personality: newPersonality
      });
    }
  };

  // Xử lý khi nhấn Enter trong input tính cách
  const handlePersonalityInputKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddPersonality();
    }
  };

  return (
    <ConfigComponentWrapper
      componentId="profile"
      title={
        <div className="flex items-center">
          <Icon name="user" size="md" className="mr-2" />
          <span>Cấu hình Profile</span>
        </div>
      }
    >
      <div className="p-4 space-y-6">
        {/* Grid chính chia thành 12 cột */}
        <div className="grid grid-cols-12 gap-6">
          {/* Cột 8-12: Ngày sinh (chiếm 5 cột) */}
          <div className="col-span-6">
            <label htmlFor="birthDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Ngày sinh
            </label>
            <DatePicker
              value={new Date(profileData.birthDate)}
              onChange={handleDateChange}
              format="dd/MM/yyyy"
              placeholder="Chọn ngày sinh"
              className="w-full"
            />
          </div>

          {/* Hàng 2 */
        /* Cột 1-2: Avatar (đã được xử lý bằng row-span-4) */}

          {/* Cột 3-7: Giới tính (chiếm 5 cột) */}
          <div className="col-span-6">
            <label htmlFor="gender" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Giới tính
            </label>
            <Select
              options={[
                { value: 'Nam', label: 'Nam' },
                { value: 'Nữ', label: 'Nữ' },
                { value: 'Khác', label: 'Khác' }
              ]}
              value={profileData.gender}
              onChange={(value) => handleSelectChange('gender', value)}
              placeholder="Chọn giới tính"
              className="w-full"
            />
          </div>

          {/* Cột 8-12: Trình độ học vấn (chiếm 5 cột) */}
          <div className="col-span-6">
            <label htmlFor="education" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Trình độ học vấn
            </label>
            <Select
              options={[
                { value: 'Trung học', label: 'Trung học' },
                { value: 'Cao đẳng', label: 'Cao đẳng' },
                { value: 'Đại học', label: 'Đại học' },
                { value: 'Sau đại học', label: 'Sau đại học' }
              ]}
              value={profileData.education}
              onChange={(value) => handleSelectChange('education', value)}
              placeholder="Chọn trình độ học vấn"
              className="w-full"
            />
          </div>

          {/* Hàng 3 */
        /* Cột 1-2: Avatar (đã được xử lý bằng row-span-4) */}

          {/* Cột 3-7: Ngôn ngữ (chiếm 5 cột) */}
          <div className="col-span-6">
            <label htmlFor="language" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Ngôn ngữ
            </label>
            <Select
              options={[
                { value: 'Việt nam', label: 'Việt nam' },
                { value: 'English', label: 'English' },
                { value: '中文', label: '中文' }
              ]}
              value={profileData.language}
              onChange={(value) => handleSelectChange('language', value)}
              placeholder="Chọn ngôn ngữ"
              className="w-full"
            />
          </div>

          {/* Cột 8-12: Quốc gia (chiếm 5 cột) */}
          <div className="col-span-6">
            <label htmlFor="country" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Quốc gia
            </label>
            <Select
              options={[
                { value: 'Việt nam', label: 'Việt nam' },
                { value: 'United States', label: 'United States' },
                { value: 'China', label: 'China' },
                { value: 'Japan', label: 'Japan' }
              ]}
              value={profileData.country}
              onChange={(value) => handleSelectChange('country', value)}
              placeholder="Chọn quốc gia"
              className="w-full"
            />
          </div>

          {/* Hàng 4 */
        /* Cột 1-2: Avatar (đã được xử lý bằng row-span-4) */}

          {/* Cột 3-7: Chức vụ (chiếm 5 cột) */}
          <div className="col-span-6">
            <label htmlFor="position" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Chức vụ
            </label>
            <Input
              id="position"
              name="position"
              value={profileData.position}
              onChange={handleInputChange}
              placeholder="Nhập chức vụ"
              className="w-full"
            />
          </div>

          {/* Cột 8-12: Kỹ năng (chiếm 5 cột) */}
          <div className="col-span-6">
            <label htmlFor="skills" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Kỹ năng
            </label>
            <div className="flex flex-wrap gap-2 mb-2 min-h-[40px] dark:border-gray-700 rounded-md p-2">
              {profileData.skills.map((skill, index) => (
                <Chip
                  key={`skill-${index}`}
                  variant="default"
                  closable
                  onClose={() => handleRemoveSkill(skill)}
                >
                  {skill}
                </Chip>
              ))}
            </div>
            <div>
              <div className='w-full'>
                <Input
                  id="skillInput"
                  value={skillInput}
                  onChange={(e) => setSkillInput(e.target.value)}
                  onKeyDown={handleSkillInputKeyDown}
                  placeholder="Nhập nhãn và ấn enter"
                  className="w-full"
                />
              </div>
            </div>
          </div>

          {/* Hàng 5 (thêm hàng mới) */}
          {/* Cột 1-12: Tính cách (chiếm 12 cột) */}
          <div className="col-span-6">
            <label htmlFor="personality" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Tính cách
            </label>
            <div className="flex flex-wrap gap-2 mb-2 min-h-[40px] dark:border-gray-700 rounded-md p-2">
              {profileData.personality.split(',').map((trait, index) => (
                trait.trim() && (
                  <Chip
                    key={`trait-${index}`}
                    variant="default"
                    closable
                    onClose={() => handleRemovePersonality(trait.trim())}
                  >
                    {trait.trim()}
                  </Chip>
                )
              ))}
            </div>
            <div>
              <div className='w-full'>
                <Input
                  id="personalityInput"
                  value={personalityInput}
                  onChange={(e) => setPersonalityInput(e.target.value)}
                  onKeyDown={handlePersonalityInputKeyDown}
                  placeholder="Nhập nhãn và ấn enter"
                  className="w-full"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </ConfigComponentWrapper>
  );
};

export default ProfileConfig;
