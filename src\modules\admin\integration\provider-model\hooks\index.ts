/**
 * Admin Provider Model Hooks
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AdminProviderModelService } from '../services';
import {
  ProviderModelQueryParams,
  CreateProviderModelDto,
  UpdateProviderModelDto,
} from '../types';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Query Keys
 */
export const adminProviderModelQueryKeys = {
  all: ['admin', 'integration', 'provider-models'] as const,
  lists: () => [...adminProviderModelQueryKeys.all, 'list'] as const,
  list: (params?: ProviderModelQueryParams) => [...adminProviderModelQueryKeys.lists(), params] as const,
  details: () => [...adminProviderModelQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...adminProviderModelQueryKeys.details(), id] as const,
};

/**
 * Hook để lấy danh sách admin provider models
 */
export const useAdminProviderModels = (params?: ProviderModelQueryParams) => {
  return useQuery({
    queryKey: adminProviderModelQueryKeys.list(params),
    queryFn: () => AdminProviderModelService.getProviderModels(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook để lấy thông tin chi tiết admin provider model
 */
export const useAdminProviderModel = (id: string, enabled = true) => {
  return useQuery({
    queryKey: adminProviderModelQueryKeys.detail(id),
    queryFn: () => AdminProviderModelService.getProviderModel(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook để tạo admin provider model mới
 */
export const useCreateAdminProviderModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProviderModelDto) => AdminProviderModelService.createProviderModel(data),
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: adminProviderModelQueryKeys.lists() });

      // Show success notification
      NotificationUtil.success({
        message: 'Tạo Provider Model thành công',
        duration: 3000,
      });
    },
    onError: (error) => {
      console.error('Error creating provider model:', error);

      // Show error notification
      NotificationUtil.error({
        message: 'Tạo Provider Model thất bại',
        duration: 5000,
      });
    },
  });
};

/**
 * Hook để cập nhật admin provider model
 */
export const useUpdateAdminProviderModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateProviderModelDto }) =>
      AdminProviderModelService.updateProviderModel(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: adminProviderModelQueryKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: adminProviderModelQueryKeys.lists() });

      // Show success notification
      NotificationUtil.success({
        message: 'Cập nhật Provider Model thành công',
        duration: 3000,
      });
    },
    onError: (error) => {
      console.error('Error updating provider model:', error);

      // Show error notification
      NotificationUtil.error({
        message: 'Cập nhật Provider Model thất bại',
        duration: 5000,
      });
    },
  });
};

/**
 * Hook để xóa admin provider model
 */
export const useDeleteAdminProviderModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => AdminProviderModelService.deleteProviderModel(id),
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: adminProviderModelQueryKeys.lists() });

      // Show success notification
      NotificationUtil.success({
        message: 'Xóa Provider Model thành công',
        duration: 3000,
      });
    },
    onError: (error) => {
      console.error('Error deleting provider model:', error);

      // Show error notification
      NotificationUtil.error({
        message: 'Xóa Provider Model thất bại',
        duration: 5000,
      });
    },
  });
};

/**
 * Hook để xóa nhiều provider models
 */
export const useDeleteMultipleProviderModels = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => AdminProviderModelService.deleteMultipleProviderModels(ids),
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: adminProviderModelQueryKeys.lists() });

      // Show success notification
      NotificationUtil.success({
        message: 'Xóa Provider Models thành công',
        duration: 3000,
      });
    },
    onError: (error) => {
      console.error('Error deleting multiple provider models:', error);

      // Show error notification
      NotificationUtil.error({
        message: 'Xóa Provider Models thất bại',
        duration: 5000,
      });
    },
  });
};
