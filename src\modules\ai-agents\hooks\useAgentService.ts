import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { useMutation, useQuery, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import {
  AgentDetailDto,
  AgentListResponse,
  CreateAgentDto,
  CreateAgentModularDto,

  GetAgentsQueryDto,
  GetTypeAgentsQueryDto,
  TypeAgentDetailDto,
  TypeAgentListResponse,
  UpdateAgentDto
} from '../types';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import {
  createAgentWithBusinessLogic,
  createAgentModularWithBusinessLogic,
  deleteAgentWithBusinessLogic,
  getAgentDetailWithBusinessLogic,
  getAgentsWithBusinessLogic,
  updateAgentWithBusinessLogic
} from '../services/agent.service';
import {
  getTypeAgentDetailWithBusinessLogic,
  getTypeAgentsForSelection,
  getTypeAgentsWithBusinessLogic
} from '../services/type-agent.service';

/**
 * Hook sử dụng services layer với business logic
 * Theo pattern của blog module
 */

/**
 * Hook để lấy danh sách agents với business logic
 */
export const useGetAgentsWithService = (
  params?: GetAgentsQueryDto,
  options?: UseQueryOptions<ApiResponse<AgentListResponse>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.AGENT_LIST, params],
    queryFn: () => getAgentsWithBusinessLogic(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để lấy chi tiết agent với business logic
 */
export const useGetAgentDetailWithService = (
  id: string | undefined,
  options?: UseQueryOptions<ApiResponse<AgentDetailDto>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, id],
    queryFn: () => getAgentDetailWithBusinessLogic(id as string),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để tạo agent mới với business logic (Legacy)
 */
export const useCreateAgentWithService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.CREATE_AGENT],
    mutationFn: (data: CreateAgentDto) => createAgentWithBusinessLogic(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_LIST] });
    },
  });
};

/**
 * Hook để tạo agent mới với cấu trúc modular (Recommended)
 */
export const useCreateAgentModularWithService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.CREATE_AGENT_MODULAR],
    mutationFn: (data: CreateAgentModularDto) => createAgentModularWithBusinessLogic(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_LIST] });
    },
  });
};

/**
 * Hook để cập nhật agent với business logic
 */
export const useUpdateAgentWithService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.UPDATE_AGENT],
    mutationFn: ({ id, data }: { id: string; data: UpdateAgentDto }) =>
      updateAgentWithBusinessLogic(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, id] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_LIST] });
    },
  });
};

/**
 * Hook để xóa agent với business logic
 */
export const useDeleteAgentWithService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.DELETE_AGENT],
    mutationFn: (id: string) => deleteAgentWithBusinessLogic(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, id] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_LIST] });
    },
  });
};

/**
 * Hook để lấy danh sách type agents với business logic
 */
export const useGetTypeAgentsWithService = (
  params?: GetTypeAgentsQueryDto,
  options?: UseQueryOptions<ApiResponse<TypeAgentListResponse>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.TYPE_AGENT_LIST, params],
    queryFn: () => getTypeAgentsWithBusinessLogic(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

/**
 * Hook để lấy type agents cho selection dropdown
 */
export const useGetTypeAgentsForSelection = (
  options?: UseQueryOptions<ApiResponse<TypeAgentListResponse>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.TYPE_AGENT_LIST, 'selection'],
    queryFn: () => getTypeAgentsForSelection(),
    staleTime: 15 * 60 * 1000, // 15 minutes (longer cache for selection data)
    ...options,
  });
};

/**
 * Hook để lấy chi tiết type agent với business logic
 */
export const useGetTypeAgentDetailWithService = (
  id: number | undefined,
  options?: UseQueryOptions<ApiResponse<TypeAgentDetailDto>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.TYPE_AGENT_DETAIL, id],
    queryFn: () => getTypeAgentDetailWithBusinessLogic(id as number),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

// Removed: useCreateTypeAgentWithService hook
