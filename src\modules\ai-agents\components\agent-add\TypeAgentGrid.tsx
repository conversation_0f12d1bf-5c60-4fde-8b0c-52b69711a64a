import React from 'react';
import { ResponsiveGrid } from '@/shared/components/common';
import TypeAgentCard, { TypeAgent } from './TypeAgentCard';

export interface TypeAgentGridProps {
  /**
   * Mảng các type agent để hiển thị
   */
  agents?: TypeAgent[];

  /**
   * ID của agent đ<PERSON><PERSON><PERSON> chọn
   */
  selectedAgentId?: number | null;

  /**
   * Có đang chọn custom agent không
   */
  isCustomAgentSelected?: boolean;

  /**
   * Hàm xử lý khi chọn agent
   */
  onSelectAgent?: (id: number) => void;

  /**
   * Hàm xử lý khi xem chi tiết agent
   */
  onViewAgent?: (id: number) => void;

  /**
   * Có hiển thị action buttons không
   */
  showActions?: boolean;
}

/**
 * Component hiển thị grid các Type Agent
 */
const TypeAgentGrid: React.FC<TypeAgentGridProps> = ({
  agents,
  selectedAgentId = null,
  onSelectAgent,
  onViewAgent,
  showActions = false
}) => {

  // Sử dụng dữ liệu được truyền vào và đảm bảo unique items
  const displayAgents = React.useMemo(() => {
    if (!agents || agents.length === 0) return [];

    // Loại bỏ duplicate items dựa trên ID
    const uniqueAgents = agents.filter((agent, index, self) =>
      index === self.findIndex(a => a.id === agent.id)
    );

    return uniqueAgents;
  }, [agents]);

  // Xử lý khi chọn agent
  const handleSelectAgent = (id: number) => {
    if (onSelectAgent) {
      onSelectAgent(id);
    }
  };

  return (
    <ResponsiveGrid
      maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4, xl: 5 }}
      maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 4 }}
      gap={6}
      className="w-full"
    >
      {/* Các Type Agent Cards */}
      {displayAgents.map((agent) => (
        <TypeAgentCard
          key={agent.id}
          agent={agent}
          isSelected={selectedAgentId === agent.id}
          onClick={handleSelectAgent}
          onView={onViewAgent}
          showActions={showActions}
        />
      ))}
    </ResponsiveGrid>
  );
};

export default TypeAgentGrid;
