import { Icon, Pagination } from '@/shared/components/common';
import { ConfigComponentWrapper } from './ConfigComponentWrapper';
import React, { useState, useMemo } from 'react';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ModernMenuItem } from '@/shared/components/common/ModernMenu';

// Interface cho dữ liệu chiến lược
interface Strategy {
  id: string;
  name: string;
  description: string;
  icon?: string;
  type: 'basic' | 'advanced' | 'custom';
}

// Interface cho dữ liệu cấu hình - chỉ cần strategyId
interface StrategyConfigData {
  strategyId: string | null; // null khi không chọn strategy nào
}

interface StrategyConfigProps {
  initialData?: StrategyConfigData;
  onSave?: (data: StrategyConfigData) => void;
}

// Interface cho query parameters
interface StrategyQueryParams {
  page: number;
  limit: number;
  search?: string;
  type?: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

/**
 * Component hiển thị một card chiến lược
 */
const StrategyCard: React.FC<{
  strategy: Strategy;
  isSelected: boolean;
  onClick: () => void;
}> = ({ strategy, isSelected, onClick }) => {
  // Xác định màu nền dựa trên loại chiến lược
  const getBgColorClass = () => {
    switch (strategy.type) {
      case 'basic':
        return 'bg-blue-50 dark:bg-blue-900/10';
      case 'advanced':
        return 'bg-purple-50 dark:bg-purple-900/10';
      case 'custom':
        return 'bg-green-50 dark:bg-green-900/10';
      default:
        return 'bg-gray-50 dark:bg-gray-800/10';
    }
  };

  // Xác định màu viền khi được chọn
  const getBorderClass = () => {
    if (!isSelected) return 'border-gray-200 dark:border-gray-700';

    switch (strategy.type) {
      case 'basic':
        return 'border-blue-500 dark:border-blue-400';
      case 'advanced':
        return 'border-purple-500 dark:border-purple-400';
      case 'custom':
        return 'border-green-500 dark:border-green-400';
      default:
        return 'border-primary dark:border-primary';
    }
  };

  // Xác định icon dựa trên loại chiến lược
  const getIconName = () => {
    if (strategy.icon) return strategy.icon;

    switch (strategy.type) {
      case 'basic':
        return 'chat';
      case 'advanced':
        return 'settings';
      case 'custom':
        return 'code';
      default:
        return 'document';
    }
  };

  return (
    <div
      className={`flex flex-col p-4 ${getBgColorClass()} rounded-lg border-2 ${getBorderClass()} cursor-pointer transition-all hover:shadow-md ${isSelected ? 'shadow-md' : ''}`}
      onClick={onClick}
    >
      {/* Icon/Header */}
      <div className="flex items-center mb-3">
        <div className="w-10 h-10 rounded-full bg-white dark:bg-gray-800 flex items-center justify-center mr-3 flex-shrink-0 border border-gray-200 dark:border-gray-700">
          <Icon
            name={getIconName()}
            size="md"
            className={`text-${strategy.type === 'basic' ? 'blue' : strategy.type === 'advanced' ? 'purple' : 'green'}-600`}
          />
        </div>
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100">{strategy.name}</h4>
      </div>

      {/* Mô tả */}
      <p className="text-sm text-gray-500 dark:text-gray-400 flex-grow mb-2">{strategy.description}</p>

      {/* Indicator khi được chọn */}
      {isSelected && (
        <div className="flex items-center justify-end mt-2">
          <div className="flex items-center text-primary">
            <Icon name="check-circle" size="sm" className="mr-1" />
            <span className="text-xs font-medium">Đã chọn</span>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Component cấu hình chiến lược cho Agent
 */
const StrategyConfig: React.FC<StrategyConfigProps> = ({
  initialData,
  onSave
}) => {
  // Danh sách chiến lược mẫu (mở rộng để test phân trang)
  const allStrategies: Strategy[] = useMemo(() => [
    {
      id: 'strategy1',
      name: 'Chiến lược cơ bản',
      description: 'Chiến lược đơn giản với các cài đặt mặc định',
      type: 'basic'
    },
    {
      id: 'strategy2',
      name: 'Chiến lược nâng cao',
      description: 'Chiến lược với các tùy chọn nâng cao và xử lý phức tạp',
      type: 'advanced'
    },
    {
      id: 'strategy3',
      name: 'Chiến lược tùy chỉnh',
      description: 'Tạo chiến lược riêng với các cài đặt tùy chỉnh hoàn toàn',
      type: 'custom'
    },
    {
      id: 'strategy4',
      name: 'Chiến lược bán hàng',
      description: 'Tối ưu hóa cho việc bán hàng và chuyển đổi khách hàng',
      type: 'advanced'
    },
    {
      id: 'strategy5',
      name: 'Chiến lược hỗ trợ',
      description: 'Chuyên biệt cho việc hỗ trợ khách hàng và giải đáp thắc mắc',
      type: 'basic'
    },
    {
      id: 'strategy6',
      name: 'Chiến lược marketing',
      description: 'Tập trung vào marketing và quảng bá sản phẩm',
      type: 'advanced'
    },
    {
      id: 'strategy7',
      name: 'Chiến lược giáo dục',
      description: 'Dành cho việc giảng dạy và truyền đạt kiến thức',
      type: 'custom'
    },
    {
      id: 'strategy8',
      name: 'Chiến lược phân tích',
      description: 'Phân tích dữ liệu và đưa ra insights',
      type: 'advanced'
    }
  ], []);

  // State cho chiến lược được chọn
  const [selectedStrategyId, setSelectedStrategyId] = useState<string | null>(
    initialData?.strategyId || null
  );

  // State cho query parameters
  const [queryParams, setQueryParams] = useState<StrategyQueryParams>({
    page: 1,
    limit: 6,
    search: '',
    type: '',
    sortBy: 'name',
    sortDirection: 'asc'
  });

  // State cho loading
  const [isLoading] = useState(false);

  // Filtered và sorted strategies
  const filteredAndSortedStrategies = useMemo(() => {
    let filtered = allStrategies;

    // Filter by search
    if (queryParams.search) {
      filtered = filtered.filter(strategy =>
        strategy.name.toLowerCase().includes(queryParams.search!.toLowerCase()) ||
        strategy.description.toLowerCase().includes(queryParams.search!.toLowerCase())
      );
    }

    // Filter by type
    if (queryParams.type) {
      filtered = filtered.filter(strategy => strategy.type === queryParams.type);
    }

    // Sort
    filtered.sort((a, b) => {
      let aValue: string | number = '';
      let bValue: string | number = '';

      switch (queryParams.sortBy) {
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'type':
          aValue = a.type;
          bValue = b.type;
          break;
        default:
          aValue = a.name;
          bValue = b.name;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return queryParams.sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      return 0;
    });

    return filtered;
  }, [allStrategies, queryParams]);

  // Paginated strategies
  const paginatedStrategies = useMemo(() => {
    const startIndex = (queryParams.page - 1) * queryParams.limit;
    const endIndex = startIndex + queryParams.limit;
    return filteredAndSortedStrategies.slice(startIndex, endIndex);
  }, [filteredAndSortedStrategies, queryParams.page, queryParams.limit]);

  // Total pages
  const totalPages = Math.ceil(filteredAndSortedStrategies.length / queryParams.limit);

  // Selected strategy object
  const selectedStrategy = allStrategies.find(s => s.id === selectedStrategyId);

  // Xử lý khi chọn một chiến lược
  const handleSelectStrategy = (strategyId: string) => {
    const newStrategyId = selectedStrategyId === strategyId ? null : strategyId;
    setSelectedStrategyId(newStrategyId);

    // Tự động lưu khi thay đổi
    if (onSave) {
      onSave({ strategyId: newStrategyId });
    }
  };

  // Xử lý search
  const handleSearch = (searchTerm: string) => {
    setQueryParams(prev => ({
      ...prev,
      search: searchTerm,
      page: 1 // Reset về trang 1 khi search
    }));
  };

  // Xử lý filter menu items
  const filterMenuItems: ModernMenuItem[] = [
    {
      id: 'all',
      label: 'Tất cả loại',
      icon: 'layers',
      onClick: () => setQueryParams(prev => ({ ...prev, type: '', page: 1 }))
    },
    {
      id: 'basic',
      label: 'Cơ bản',
      icon: 'circle',
      onClick: () => setQueryParams(prev => ({ ...prev, type: 'basic', page: 1 }))
    },
    {
      id: 'advanced',
      label: 'Nâng cao',
      icon: 'settings',
      onClick: () => setQueryParams(prev => ({ ...prev, type: 'advanced', page: 1 }))
    },
    {
      id: 'custom',
      label: 'Tùy chỉnh',
      icon: 'code',
      onClick: () => setQueryParams(prev => ({ ...prev, type: 'custom', page: 1 }))
    },
    {
      id: 'divider-1',
      divider: true
    },
    {
      id: 'sort-name-asc',
      label: 'Sắp xếp: Tên A-Z',
      icon: 'arrow-up-a-z',
      onClick: () => setQueryParams(prev => ({ ...prev, sortBy: 'name', sortDirection: 'asc' }))
    },
    {
      id: 'sort-name-desc',
      label: 'Sắp xếp: Tên Z-A',
      icon: 'arrow-down-z-a',
      onClick: () => setQueryParams(prev => ({ ...prev, sortBy: 'name', sortDirection: 'desc' }))
    },
    {
      id: 'sort-type-asc',
      label: 'Sắp xếp: Loại A-Z',
      icon: 'arrow-up-narrow-wide',
      onClick: () => setQueryParams(prev => ({ ...prev, sortBy: 'type', sortDirection: 'asc' }))
    }
  ];

  // Xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    setQueryParams(prev => ({ ...prev, page }));
  };

  return (
    <ConfigComponentWrapper
      componentId="strategy"
      title={
        <div className="flex items-center">
          <Icon name="workflow" size="md" className="mr-2" />
          <span>Chiến lược</span>
        </div>
      }
    >
      <div className="p-4 space-y-6">
        {/* Tiêu đề chính */}
        <div className="mb-6 text-center">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Chọn chiến lược cho Agent
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Chọn một chiến lược mong muốn hoặc để trống nếu không cần
          </p>
        </div>

        {/* Thông tin chiến lược đã chọn - Đẩy lên trên */}
        {selectedStrategy && (
          <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/10 border border-green-200 dark:border-green-800 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Icon name="check-circle" size="md" className="text-green-600 mr-3" />
                <div>
                  <h4 className="text-sm font-medium text-green-800 dark:text-green-200">
                    Đã chọn: {selectedStrategy.name}
                  </h4>
                  <p className="text-xs text-green-600 dark:text-green-300">
                    {selectedStrategy.description}
                  </p>
                  <span className="inline-block mt-1 px-2 py-1 text-xs bg-green-100 dark:bg-green-800 text-green-700 dark:text-green-200 rounded">
                    {selectedStrategy.type === 'basic' ? 'Cơ bản' :
                     selectedStrategy.type === 'advanced' ? 'Nâng cao' : 'Tùy chỉnh'}
                  </span>
                </div>
              </div>
              <button
                onClick={() => handleSelectStrategy(selectedStrategy.id)}
                className="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200"
                title="Bỏ chọn chiến lược"
              >
                <Icon name="x" size="sm" />
              </button>
            </div>
          </div>
        )}

        {/* Thông báo khi chưa chọn */}
        {!selectedStrategyId && (
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg text-center">
            <Icon name="info" size="md" className="text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Chưa chọn chiến lược nào. Agent sẽ sử dụng cài đặt mặc định.
            </p>
          </div>
        )}

        {/* Menu bar với search và filter */}
        <MenuIconBar
          onSearch={handleSearch}
          items={filterMenuItems}
          isLoading={isLoading}
          showDateFilter={false}
          showColumnFilter={false}
        />

        {/* Danh sách chiến lược với phân trang */}
        <div className="space-y-4">
          {paginatedStrategies.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {paginatedStrategies.map(strategy => (
                  <StrategyCard
                    key={strategy.id}
                    strategy={strategy}
                    isSelected={selectedStrategyId === strategy.id}
                    onClick={() => handleSelectStrategy(strategy.id)}
                  />
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-6">
                  <Pagination
                    currentPage={queryParams.page}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                    variant="simple"
                    showItemsPerPageSelector={false}
                  />
                </div>
              )}

              {/* Thông tin tổng quan */}
              <div className="text-center text-sm text-gray-500 dark:text-gray-400">
                Hiển thị {paginatedStrategies.length} trong tổng số {filteredAndSortedStrategies.length} chiến lược
                {queryParams.search && ` (tìm kiếm: "${queryParams.search}")`}
                {queryParams.type && ` (loại: ${queryParams.type})`}
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <Icon name="search" size="lg" className="text-gray-400 mx-auto mb-3" />
              <p className="text-gray-500 dark:text-gray-400">
                Không tìm thấy chiến lược nào phù hợp
              </p>
              {(queryParams.search || queryParams.type) && (
                <button
                  onClick={() => setQueryParams(prev => ({ ...prev, search: '', type: '', page: 1 }))}
                  className="mt-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm"
                >
                  Xóa bộ lọc
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </ConfigComponentWrapper>
  );
};

export default StrategyConfig;

// Export các interface để có thể sử dụng ở các file khác
export type { Strategy, StrategyConfigData, StrategyConfigProps };

