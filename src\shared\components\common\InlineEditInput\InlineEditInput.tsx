import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Icon } from '@/shared/components/common';
import { cn } from '@/shared/utils';

interface InlineEditInputProps {
  /**
   * Giá trị hiện tại
   */
  value: string;

  /**
   * Callback khi lưu giá trị mới
   */
  onSave: (newValue: string) => void;

  /**
   * Placeholder khi không có giá trị
   */
  placeholder?: string;

  /**
   * CSS classes bổ sung
   */
  className?: string;

  /**
   * Vô hiệu hóa editing
   */
  disabled?: boolean;

  /**
   * Độ dài tối đa
   */
  maxLength?: number;

  /**
   * Validation function
   */
  validate?: (value: string) => string | null;

  /**
   * Loading state
   */
  isLoading?: boolean;

  /**
   * Typography variant để match với design system
   */
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2';

  /**
   * Auto width - input sẽ có width theo nội dung thay vì full width
   */
  autoWidth?: boolean;

  /**
   * Text wrap - cho phép text xuống dòng thay vì overflow
   */
  textWrap?: boolean;

  /**
   * Căn giữa text (center alignment)
   */
  centerAligned?: boolean;

  /**
   * Không hiển thị underline khi edit
   */
  noUnderline?: boolean;

  /**
   * Width tự động theo content với giới hạn min/max
   */
  dynamicWidth?: boolean;

  /**
   * Độ rộng tối đa (VD: "300px", "20rem")
   */
  maxWidth?: string;

  /**
   * Độ rộng tối thiểu (VD: "120px", "8rem")
   */
  minWidth?: string;
}

/**
 * Component input inline editing với thiết kế minimalist
 * - Double-click để edit
 * - Chỉ có underline khi edit
 * - Nút X (hủy) và ✓ (lưu) ở cuối
 */
const InlineEditInput: React.FC<InlineEditInputProps> = ({
  value,
  onSave,
  placeholder = 'Nhập tên...',
  className,
  disabled = false,
  maxLength = 100,
  validate,
  isLoading = false,
  variant = 'body1',
  autoWidth = false,
  textWrap = false,
  centerAligned = false,
  noUnderline = false,
  dynamicWidth = false,
  maxWidth = '400px',
  minWidth = '120px',
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const [error, setError] = useState<string | null>(null);
  const [inputWidth, setInputWidth] = useState<number | undefined>(undefined);
  const [dynamicInputWidth, setDynamicInputWidth] = useState<number | undefined>(undefined);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const measureRef = useRef<HTMLSpanElement>(null);

  // Text measurement function
  const measureTextWidth = useCallback((text: string, fontSize: string, fontFamily: string): number => {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (context) {
      context.font = `${fontSize} ${fontFamily}`;
      return context.measureText(text).width;
    }
    // Fallback estimation
    return text.length * 8;
  }, []);

  // Convert CSS units to pixels
  const convertToPixels = useCallback((value: string): number => {
    if (value.endsWith('px')) {
      return parseInt(value, 10);
    }
    if (value.endsWith('rem')) {
      return parseInt(value, 10) * 16; // Assume 1rem = 16px
    }
    if (value.endsWith('em')) {
      return parseInt(value, 10) * 16; // Assume 1em = 16px
    }
    return parseInt(value, 10) || 120; // Default fallback
  }, []);

  // Reset edit value khi value prop thay đổi
  useEffect(() => {
    setEditValue(value);
  }, [value]);

  // Focus input khi chuyển sang edit mode
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select(); // Auto-select text
    }
  }, [isEditing]);

  // Tính toán width cho autoWidth mode
  useEffect(() => {
    if (autoWidth && measureRef.current) {
      const textToMeasure = editValue || placeholder;
      measureRef.current.textContent = textToMeasure;
      const measuredWidth = measureRef.current.offsetWidth;
      setInputWidth(Math.max(measuredWidth + 40, 100)); // +40 cho padding, min 100px
    }
  }, [autoWidth, editValue, placeholder]);

  // Tính toán width cho dynamicWidth mode
  useEffect(() => {
    if (dynamicWidth) {
      const textToMeasure = editValue || placeholder || 'A'; // Fallback để có width tối thiểu

      // Get computed font properties
      const computedStyle = window.getComputedStyle(document.body);
      const fontSize = computedStyle.fontSize || '16px';
      const fontFamily = computedStyle.fontFamily || 'system-ui, sans-serif';

      // Measure text width
      const textWidth = measureTextWidth(textToMeasure, fontSize, fontFamily);

      // Add padding for cursor and editing space
      const paddingWidth = 60; // Space for cursor, padding, and buttons
      const calculatedWidth = textWidth + paddingWidth;

      // Apply min/max constraints
      const minWidthPx = convertToPixels(minWidth);
      const maxWidthPx = convertToPixels(maxWidth);

      const finalWidth = Math.min(Math.max(calculatedWidth, minWidthPx), maxWidthPx);
      setDynamicInputWidth(finalWidth);
    }
  }, [dynamicWidth, editValue, placeholder, measureTextWidth, convertToPixels, minWidth, maxWidth]);

  // Xử lý double-click để bắt đầu edit
  const handleDoubleClick = useCallback(() => {
    if (disabled || isLoading) return;
    setIsEditing(true);
    setError(null);
  }, [disabled, isLoading]);

  // Xử lý thay đổi giá trị
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setEditValue(newValue);

    // Validate real-time
    if (validate) {
      const validationError = validate(newValue);
      setError(validationError);
    }
  }, [validate]);

  // Xử lý lưu
  const handleSave = useCallback(() => {
    if (error) return;

    const trimmedValue = editValue.trim();

    // Validate trước khi lưu
    if (validate) {
      const validationError = validate(trimmedValue);
      if (validationError) {
        setError(validationError);
        return;
      }
    }

    onSave(trimmedValue);
    setIsEditing(false);
    setError(null);
  }, [editValue, error, validate, onSave]);

  // Xử lý hủy
  const handleCancel = useCallback(() => {
    setEditValue(value); // Reset về giá trị ban đầu
    setIsEditing(false);
    setError(null);
  }, [value]);

  // Xử lý keyboard events
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !textWrap) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  }, [handleSave, handleCancel, textWrap]);

  // Click outside để hủy edit
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isEditing &&
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        handleCancel();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isEditing, handleCancel]);

  // Typography classes based on variant
  const getTypographyClasses = () => {
    const baseClasses = 'font-medium';

    switch (variant) {
      case 'h1':
        return `${baseClasses} text-4xl`;
      case 'h2':
        return `${baseClasses} text-3xl`;
      case 'h3':
        return `${baseClasses} text-2xl`;
      case 'h4':
        return `${baseClasses} text-xl`;
      case 'h5':
        return `${baseClasses} text-lg`;
      case 'h6':
        return `${baseClasses} text-base`;
      case 'body1':
        return `${baseClasses} text-base`;
      case 'body2':
        return `${baseClasses} text-sm`;
      default:
        return `${baseClasses} text-base`;
    }
  };

  const typographyClasses = getTypographyClasses();

  if (isEditing) {
    const InputComponent = textWrap ? 'textarea' : 'input';

    // Determine width style
    const widthStyle: React.CSSProperties = {};
    if (dynamicWidth && dynamicInputWidth) {
      widthStyle.width = `${dynamicInputWidth}px`;
    } else if (autoWidth && inputWidth) {
      widthStyle.width = `${inputWidth}px`;
    }

    // Container classes for different modes
    const containerClasses = cn(
      'relative',
      // Width handling
      dynamicWidth || autoWidth ? 'inline-block' : 'w-full',
      // Center alignment for dynamic width
      centerAligned && (dynamicWidth || autoWidth) ? 'mx-auto' : '',
      className
    );

    // Wrapper for centering
    const wrapperClasses = cn(
      centerAligned ? 'flex justify-center w-full' : 'w-full'
    );

    return (
      <div className={wrapperClasses}>
        <div ref={containerRef} className={containerClasses}>
          <div className="relative">
            <InputComponent
              ref={inputRef as React.RefObject<HTMLInputElement & HTMLTextAreaElement>}
              type={textWrap ? undefined : "text"}
              value={editValue}
              onChange={handleChange}
              onKeyDown={handleKeyDown}
              maxLength={maxLength}
              placeholder={placeholder}
              disabled={isLoading}
              rows={textWrap ? 3 : undefined}
              style={widthStyle}
              className={cn(
                // Base styles
                'bg-transparent outline-none resize-none transition-all duration-200',
                // Border styles
                noUnderline ? 'border-0' : 'border-b-2 border-primary',
                // Width styles
                (dynamicWidth || autoWidth) ? 'w-auto' : 'w-full',
                // Text alignment
                centerAligned ? 'text-center' : 'text-left',
                // Padding
                'px-3 py-1',
                // Space for buttons (only if not center aligned)
                !centerAligned && 'pr-16',
                // Typography
                typographyClasses,
                'text-gray-900 dark:text-gray-100',
                // Text wrap styles
                textWrap ? 'whitespace-normal break-words' : 'whitespace-nowrap overflow-hidden text-ellipsis',
                // Focus styles
                noUnderline ? 'focus:ring-2 focus:ring-primary/20 focus:rounded-md' : 'focus:border-primary focus:ring-0',
                // Error styles
                error && (noUnderline ? 'ring-2 ring-red-500' : 'border-red-500'),
                // Loading styles
                isLoading && 'opacity-50 cursor-not-allowed'
              )}
            />

          {/* Action Buttons */}
          <div className={cn(
            'absolute top-1/2 -translate-y-1/2 flex items-center gap-1',
            centerAligned ? 'right-0 -mr-16' : 'right-0'
          )}>
            {/* Cancel Button */}
            <button
              type="button"
              onClick={handleCancel}
              disabled={isLoading}
              className={cn(
                'p-1 rounded-full transition-colors duration-200',
                'hover:bg-red-100 dark:hover:bg-red-900/20',
                'text-red-600 dark:text-red-400',
                'disabled:opacity-50 disabled:cursor-not-allowed'
              )}
              title="Hủy"
            >
              <Icon name="x" size="sm" />
            </button>

            {/* Save Button */}
            <button
              type="button"
              onClick={handleSave}
              disabled={isLoading || !!error || editValue.trim() === ''}
              className={cn(
                'p-1 rounded-full transition-colors duration-200',
                'hover:bg-green-100 dark:hover:bg-green-900/20',
                'text-green-600 dark:text-green-400',
                'disabled:opacity-50 disabled:cursor-not-allowed'
              )}
              title="Lưu"
            >
              {isLoading ? (
                <Icon name="loader-2" size="sm" className="animate-spin" />
              ) : (
                <Icon name="check" size="sm" />
              )}
            </button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mt-1 text-sm text-red-600 dark:text-red-400">
            {error}
          </div>
        )}

        {/* Character Count */}
        {maxLength && (
          <div className="mt-1 text-xs text-gray-500 dark:text-gray-400 text-right">
            {editValue.length}/{maxLength}
          </div>
        )}
        </div>
      </div>
    );
  }

  // View mode
  const viewWrapperClasses = cn(
    centerAligned ? 'flex justify-center w-full' : 'w-full'
  );

  const viewContainerClasses = cn(
    'inline-block cursor-pointer transition-all duration-200',
    'hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-md px-2 py-1',
    disabled && 'cursor-not-allowed opacity-50',
    centerAligned ? 'text-center' : 'text-left',
    className
  );

  return (
    <>
      <div className={viewWrapperClasses}>
        <div
          ref={containerRef}
          onDoubleClick={handleDoubleClick}
          className={viewContainerClasses}
          title={disabled ? undefined : 'Double-click để chỉnh sửa'}
        >
          <span className={cn(
            typographyClasses,
            'text-gray-900 dark:text-gray-100',
            !value && 'text-gray-500 dark:text-gray-400 italic'
          )}>
            {value || placeholder}
          </span>
        </div>
      </div>

      {/* Hidden span để measure text width */}
      {autoWidth && (
        <span
          ref={measureRef}
          className={cn(
            'absolute invisible whitespace-nowrap pointer-events-none',
            typographyClasses
          )}
          style={{ top: '-9999px', left: '-9999px' }}
        />
      )}
    </>
  );
};

export default InlineEditInput;
