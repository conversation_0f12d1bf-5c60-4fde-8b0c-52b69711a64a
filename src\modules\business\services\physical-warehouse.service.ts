import { apiClient as apiRequest } from '@/shared/api/';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  CreatePhysicalWarehouseDto,
  UpdatePhysicalWarehouseDto,
  PhysicalWarehouseDto,
  PhysicalWarehouseListItemDto,
  QueryPhysicalWarehouseDto,
} from '../types/physical-warehouse.types';

/**
 * Service xử lý API liên quan đến kho vật lý
 */
export const PhysicalWarehouseService = {
  /**
   * Lấy danh sách kho vật lý
   * @param params Tham số truy vấn
   * @returns Danh sách kho vật lý với phân trang
   */
  getPhysicalWarehouses: async (params?: QueryPhysicalWarehouseDto): Promise<ApiResponseDto<PaginatedResult<PhysicalWarehouseListItemDto>>> => {
    return apiRequest.get('/user/physical-warehouses', { params });
  },

  /**
   * Lấy chi tiết kho vật lý theo ID
   * @param id ID của kho vật lý
   * @returns Chi tiết kho vật lý
   */
  getPhysicalWarehouseById: async (id: number): Promise<ApiResponseDto<PhysicalWarehouseDto>> => {
    return apiRequest.get(`/user/physical-warehouses/${id}`);
  },

  /**
   * Tạo kho vật lý mới
   * @param data Dữ liệu tạo kho vật lý
   * @returns Thông tin kho vật lý đã tạo
   */
  createPhysicalWarehouse: async (data: CreatePhysicalWarehouseDto): Promise<ApiResponseDto<PhysicalWarehouseDto>> => {
    return apiRequest.post('/user/physical-warehouses', data);
  },

  /**
   * Cập nhật kho vật lý
   * @param id ID của kho vật lý
   * @param data Dữ liệu cập nhật kho vật lý
   * @returns Thông tin kho vật lý đã cập nhật
   */
  updatePhysicalWarehouse: async (id: number, data: UpdatePhysicalWarehouseDto): Promise<ApiResponseDto<PhysicalWarehouseDto>> => {
    return apiRequest.put(`/user/physical-warehouses/${id}`, data);
  },

  /**
   * Xóa kho vật lý
   * @param id ID của kho vật lý
   * @returns Thông báo xóa thành công
   */
  deletePhysicalWarehouse: async (id: number): Promise<ApiResponseDto<{ message: string }>> => {
    return apiRequest.delete(`/user/physical-warehouses/${id}`);
  },

  /**
   * Xóa nhiều kho vật lý
   * @param warehouseIds Danh sách ID các kho vật lý cần xóa
   * @returns Thông báo xóa thành công
   */
  deleteMultiplePhysicalWarehouses: async (warehouseIds: number[]): Promise<ApiResponseDto<{ message: string }>> => {
    return apiRequest.delete('/user/physical-warehouses/bulk', {
      data: { warehouseIds }
    });
  },
};
