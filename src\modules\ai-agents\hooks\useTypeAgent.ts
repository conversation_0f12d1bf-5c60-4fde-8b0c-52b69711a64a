import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import {
  getTypeAgents,
  getTypeAgentDetail,
} from '../api/agent.api';

import {
  GetTypeAgentsQueryDto,
  TypeAgentListResponse,
  TypeAgentDetailDto,
} from '../types';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';



/**
 * Hook để lấy danh sách type agents
 * @param params Query params
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetTypeAgents = (
  params?: GetTypeAgentsQueryDto,
  options?: UseQueryOptions<ApiResponse<TypeAgentListResponse>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.TYPE_AGENT_LIST, params],
    queryFn: () => getTypeAgents(params),
    staleTime: 10 * 60 * 1000, // 10 minutes (type agents ít thay đổi)
    ...options,
  });
};

/**
 * Hook để lấy chi tiết type agent
 * @param id ID của type agent
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetTypeAgentDetail = (
  id: number | undefined,
  options?: UseQueryOptions<ApiResponse<TypeAgentDetailDto>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.TYPE_AGENT_DETAIL, id],
    queryFn: () => getTypeAgentDetail(id as number),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

// Removed: useCreateTypeAgent, useUpdateTypeAgent, useDeleteTypeAgent hooks
