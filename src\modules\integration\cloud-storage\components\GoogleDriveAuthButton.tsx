import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Icon, Typography } from '@/shared/components/common';
import { useGetOAuthUrl, useHandleOAuthCallback } from '../hooks';

interface GoogleDriveAuthButtonProps {
  /**
   * Provider ID để xác thực
   */
  providerId?: number;
  
  /**
   * Callback khi xác thực thành công
   */
  onAuthSuccess?: (tokens: { accessToken: string; refreshToken: string }) => void;
  
  /**
   * Callback khi xác thực thất bại
   */
  onAuthError?: (error: Error) => void;
  
  /**
   * Hiển thị dạng compact
   */
  compact?: boolean;
  
  /**
   * Disabled state
   */
  disabled?: boolean;
  
  /**
   * Custom class
   */
  className?: string;
}

/**
 * Button xác thực Google Drive với OAuth
 */
const GoogleDriveAuthButton: React.FC<GoogleDriveAuthButtonProps> = ({
  providerId,
  onAuthSuccess,
  onAuthError,
  compact = false,
  disabled = false,
  className = '',
}) => {
  const { t } = useTranslation(['integration', 'common']);
  
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  
  const getOAuthUrlMutation = useGetOAuthUrl();
  const handleCallbackMutation = useHandleOAuthCallback();

  const handleAuth = async () => {
    if (!providerId) {
      onAuthError?.(new Error('Provider ID is required'));
      return;
    }

    try {
      setIsAuthenticating(true);

      // Lấy OAuth URL
      const urlResult = await getOAuthUrlMutation.mutateAsync(providerId);
      const authUrl = urlResult.result?.result?.authUrl;

      // Mở popup window để xác thực
      const popup = window.open(
        authUrl,
        'google-drive-auth',
        'width=500,height=600,scrollbars=yes,resizable=yes'
      );

      if (!popup) {
        throw new Error('Popup blocked. Please allow popups for this site.');
      }

      // Lắng nghe message từ popup
      const handleMessage = async (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return;

        if (event.data.type === 'GOOGLE_DRIVE_AUTH_SUCCESS') {
          const { code, state } = event.data;
          
          try {
            // Xử lý callback
            const callbackResult = await handleCallbackMutation.mutateAsync({
              providerId,
              code,
              state,
            });

            onAuthSuccess?.(callbackResult.result?.result);
            popup.close();
          } catch (error) {
            onAuthError?.(error as Error);
          }
        } else if (event.data.type === 'GOOGLE_DRIVE_AUTH_ERROR') {
          onAuthError?.(new Error(event.data.error));
          popup.close();
        }

        window.removeEventListener('message', handleMessage);
      };

      window.addEventListener('message', handleMessage);

      // Kiểm tra nếu popup bị đóng thủ công
      const checkClosed = setInterval(() => {
        if (popup.closed) {
          clearInterval(checkClosed);
          window.removeEventListener('message', handleMessage);
          setIsAuthenticating(false);
        }
      }, 1000);

    } catch (error) {
      console.error('Auth error:', error);
      onAuthError?.(error as Error);
    } finally {
      setIsAuthenticating(false);
    }
  };

  const isLoading = isAuthenticating || getOAuthUrlMutation.isPending || handleCallbackMutation.isPending;

  if (compact) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={handleAuth}
        disabled={disabled || isLoading}
        className={`flex items-center gap-2 ${className}`}
      >
        {isLoading ? (
          <Icon name="loading" size="sm" />
        ) : (
          <Icon name="cloud" size="sm" />
        )}
        {t('integration:cloudStorage.form.connectWithOAuth', { provider: 'Google Drive' })}
      </Button>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center gap-3 p-4 border rounded-lg bg-muted/50">
        <div className="flex-shrink-0">
          <Icon name="cloud" size="lg" className="text-primary" />
        </div>
        <div className="flex-1">
          <Typography variant="subtitle2" className="mb-1">
            Google Drive
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            {t('integration:cloudStorage.providers.google-drive')}
          </Typography>
        </div>
      </div>

      <Button
        variant="primary"
        onClick={handleAuth}
        disabled={disabled || isLoading}
        className="w-full"
      >
        {isLoading ? (
          <>
            <Icon name="loading" size="sm" className="mr-2" />
            {t('common:connecting')}
          </>
        ) : (
          <>
            <Icon name="cloud" size="sm" className="mr-2" />
            {t('integration:cloudStorage.form.connectWithOAuth', { provider: 'Google Drive' })}
          </>
        )}
      </Button>

      {(getOAuthUrlMutation.error || handleCallbackMutation.error) && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <Typography variant="caption" className="text-red-600">
            {getOAuthUrlMutation.error?.message || handleCallbackMutation.error?.message}
          </Typography>
        </div>
      )}
    </div>
  );
};

export default GoogleDriveAuthButton;
