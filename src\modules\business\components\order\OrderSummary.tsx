import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Divider,
  Chip,
} from '@/shared/components/common';
import { OrderCustomerDto, OrderItemDto, ShippingDto, DigitalDeliveryDto } from '../../types/order.types';
import { formatCurrency } from '@/shared/utils/format';

interface OrderSummaryProps {
  customer?: OrderCustomerDto;
  items: OrderItemDto[];
  shipping?: ShippingDto;
  digitalDelivery?: DigitalDeliveryDto;
  paymentMethod?: string;
  notes?: string;
  tags?: string[];
}

/**
 * Component tóm tắt đơn hàng
 */
const OrderSummary: React.FC<OrderSummaryProps> = ({
  customer,
  items,
  shipping,
  digitalDelivery,
  paymentMethod,
  notes,
  tags,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Tính toán các giá trị
  const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);
  const shippingFee = shipping?.fee || 0;
  const tax = 0; // TODO: Implement tax calculation
  const total = subtotal + shippingFee + tax;

  return (
    <Card>
      <div className="p-6">
        <Typography variant="h6" className="mb-4">
          {t('business:order.orderSummary')}
        </Typography>

        {/* Thông tin khách hàng */}
        {customer?.name && (
          <div className="mb-6">
            <Typography variant="subtitle2" className="mb-2 text-muted-foreground">
              {t('business:order.customerInfo')}
            </Typography>
            <div className="bg-card-muted p-3 rounded">
              <Typography variant="subtitle2">{customer.name}</Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {customer.phone} • {customer.email}
              </Typography>
              {customer.address?.address && (
                <Typography variant="body2" className="text-muted-foreground mt-1">
                  {customer.address.address}
                  {customer.address.ward && `, ${customer.address.ward}`}
                  {customer.address.district && `, ${customer.address.district}`}
                  {customer.address.province && `, ${customer.address.province}`}
                </Typography>
              )}
            </div>
          </div>
        )}

        {/* Danh sách sản phẩm */}
        <div className="mb-6">
          <Typography variant="subtitle2" className="mb-2 text-muted-foreground">
            {t('business:order.items')} ({items.length})
          </Typography>
          <div className="space-y-2">
            {items.map((item, index) => (
              <div key={index} className="flex items-center justify-between py-2">
                <div className="flex items-center gap-3">
                  {item.image && (
                    <img
                      src={item.image}
                      alt={item.productName}
                      className="w-10 h-10 object-cover rounded"
                    />
                  )}
                  <div>
                    <Typography variant="subtitle2">{item.productName}</Typography>
                    {item.variantName && (
                      <Typography variant="caption" className="text-muted-foreground">
                        {item.variantName}
                      </Typography>
                    )}
                    <Typography variant="caption" className="text-muted-foreground">
                      {formatCurrency(item.price)} × {item.quantity}
                    </Typography>
                  </div>
                </div>
                <Typography variant="subtitle2" className="font-medium">
                  {formatCurrency(item.totalPrice)}
                </Typography>
              </div>
            ))}
          </div>
        </div>

        {/* Thông tin vận chuyển */}
        {shipping && (
          <div className="mb-6">
            <Typography variant="subtitle2" className="mb-2 text-muted-foreground">
              {t('business:order.shippingInfo')}
            </Typography>
            <div className="bg-card-muted p-3 rounded">
              <div className="flex items-center justify-between mb-2">
                <Typography variant="body2">
                  {t('business:order.shippingMethod')}: {shipping.method}
                </Typography>
                <Typography variant="subtitle2" className="font-medium">
                  {formatCurrency(shipping.fee)}
                </Typography>
              </div>
              {shipping.serviceName && (
                <Typography variant="caption" className="text-muted-foreground">
                  {shipping.serviceName}
                </Typography>
              )}
              {shipping.estimatedDelivery && (
                <Typography variant="caption" className="text-muted-foreground block">
                  {t('business:order.estimatedDelivery')}: {shipping.estimatedDelivery}
                </Typography>
              )}
              {shipping.toAddress && (
                <Typography variant="caption" className="text-muted-foreground block mt-1">
                  {t('business:order.deliveryAddress')}: {shipping.toAddress.address}
                  {shipping.toAddress.ward && `, ${shipping.toAddress.ward}`}
                  {shipping.toAddress.district && `, ${shipping.toAddress.district}`}
                  {shipping.toAddress.province && `, ${shipping.toAddress.province}`}
                </Typography>
              )}
            </div>
          </div>
        )}

        {/* Thông tin giao hàng sản phẩm số */}
        {digitalDelivery && (
          <div className="mb-6">
            <Typography variant="subtitle2" className="mb-2 text-muted-foreground">
              {t('business:order.digitalDelivery.title')}
            </Typography>
            <div className="bg-card-muted p-3 rounded">
              <Typography variant="body2" className="mb-1">
                {t('business:order.digitalDelivery.method')}: {digitalDelivery.method}
              </Typography>
              <Typography variant="body2" className="mb-1">
                {t('business:order.digitalDelivery.recipient')}: {digitalDelivery.recipient}
              </Typography>
              {digitalDelivery.message && (
                <Typography variant="caption" className="text-muted-foreground block">
                  {t('business:order.digitalDelivery.message')}: {digitalDelivery.message}
                </Typography>
              )}
              {digitalDelivery.scheduledDelivery && (
                <Typography variant="caption" className="text-muted-foreground block">
                  {t('business:order.digitalDelivery.scheduledDelivery')}: {digitalDelivery.scheduledDelivery}
                </Typography>
              )}
            </div>
          </div>
        )}

        {/* Phương thức thanh toán */}
        {paymentMethod && (
          <div className="mb-6">
            <Typography variant="subtitle2" className="mb-2 text-muted-foreground">
              {t('business:order.paymentMethod')}
            </Typography>
            <div className="bg-card-muted p-3 rounded">
              <Typography variant="body2">{paymentMethod}</Typography>
            </div>
          </div>
        )}

        {/* Ghi chú */}
        {notes && (
          <div className="mb-6">
            <Typography variant="subtitle2" className="mb-2 text-muted-foreground">
              {t('business:order.notes')}
            </Typography>
            <div className="bg-card-muted p-3 rounded">
              <Typography variant="body2">{notes}</Typography>
            </div>
          </div>
        )}

        {/* Tags */}
        {tags && tags.length > 0 && (
          <div className="mb-6">
            <Typography variant="subtitle2" className="mb-2 text-muted-foreground">
              {t('business:order.tags')}
            </Typography>
            <div className="flex flex-wrap gap-1">
              {tags.map((tag, index) => (
                <Chip key={index} size="sm" outlined>
                  {tag}
                </Chip>
              ))}
            </div>
          </div>
        )}

        <Divider className="my-4" />

        {/* Tổng kết */}
        <div className="space-y-2">
          <div className="flex justify-between">
            <Typography variant="body2">{t('business:order.subtotal')}</Typography>
            <Typography variant="body2">{formatCurrency(subtotal)}</Typography>
          </div>
          
          {shippingFee > 0 && (
            <div className="flex justify-between">
              <Typography variant="body2">{t('business:order.shippingFee')}</Typography>
              <Typography variant="body2">{formatCurrency(shippingFee)}</Typography>
            </div>
          )}
          
          {tax > 0 && (
            <div className="flex justify-between">
              <Typography variant="body2">{t('business:order.tax')}</Typography>
              <Typography variant="body2">{formatCurrency(tax)}</Typography>
            </div>
          )}
          
          <Divider className="my-2" />
          
          <div className="flex justify-between">
            <Typography variant="h6" className="font-semibold">
              {t('business:order.total')}
            </Typography>
            <Typography variant="h6" className="font-semibold text-primary">
              {formatCurrency(total)}
            </Typography>
          </div>
        </div>

        {/* Thông tin bổ sung */}
        <div className="mt-4 pt-4">
          <Typography variant="caption" className="text-muted-foreground">
            {t('business:order.summaryNote')}
          </Typography>
        </div>
      </div>
    </Card>
  );
};

export default OrderSummary;
