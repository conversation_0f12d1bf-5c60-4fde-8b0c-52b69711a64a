/**
 * Hook để sử dụng AgentConfigAccordionContext
 */

import { useContext } from 'react';
import { AgentConfigAccordionContext } from '../contexts/AgentConfigAccordionContext';

export const useAgentConfigAccordion = () => {
  const context = useContext(AgentConfigAccordionContext);
  if (context === undefined) {
    throw new Error('useAgentConfigAccordion must be used within an AgentConfigAccordionProvider');
  }
  return context;
};
