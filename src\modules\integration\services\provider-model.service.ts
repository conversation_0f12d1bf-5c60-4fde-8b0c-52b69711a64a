/**
 * Provider Model Service Layer
 */

import { ProviderModelApi } from '../api/provider-model.api';
import {
  ProviderModel,
  ProviderModelListItem,
  CreateProviderModelDto,
  UpdateProviderModelDto,
  ProviderModelQueryDto,
} from '../types/provider-model.types';
import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';

/**
 * Service layer cho provider model operations
 * Cung cấp business logic và error handling
 */
export class ProviderModelService {
  /**
   * Lấy danh sách provider models với phân trang
   */
  static async getProviderModels(
    params: Partial<ProviderModelQueryDto> = {}
  ): Promise<ApiResponseDto<PaginatedResult<ProviderModelListItem>>> {
    try {
      // Validate parameters
      const validatedParams = {
        page: params.page || 1,
        limit: Math.min(params.limit || 10, 100), // Max 100 items per page
        search: params.search?.trim(),
        sortBy: params.sortBy,
        sortDirection: params.sortDirection,
        type: params.type,
      };

      // Remove undefined values
      Object.keys(validatedParams).forEach(key => {
        if (validatedParams[key as keyof typeof validatedParams] === undefined) {
          delete validatedParams[key as keyof typeof validatedParams];
        }
      });

      return await ProviderModelApi.getProviderModels(validatedParams);
    } catch (error) {
      console.error('Service error - getProviderModels:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Lấy thông tin chi tiết một provider model
   */
  static async getProviderModel(id: string): Promise<ApiResponseDto<ProviderModel>> {
    try {
      if (!id || id.trim() === '') {
        throw new Error('Provider model ID is required');
      }

      return await ProviderModelApi.getProviderModel(id.trim());
    } catch (error) {
      console.error(`Service error - getProviderModel(${id}):`, error);
      throw this.handleError(error);
    }
  }

  /**
   * Tạo provider model mới
   */
  static async createProviderModel(
    data: CreateProviderModelDto
  ): Promise<ApiResponseDto<ProviderModel>> {
    try {
      // Validate required fields
      if (!data.name || data.name.trim() === '') {
        throw new Error('Provider model name is required');
      }

      if (!data.type) {
        throw new Error('Provider type is required');
      }

      if (!data.apiKey || data.apiKey.trim() === '') {
        throw new Error('API key is required');
      }

      // Sanitize data
      const sanitizedData: CreateProviderModelDto = {
        name: data.name.trim(),
        type: data.type,
        apiKey: data.apiKey.trim(),
      };

      return await ProviderModelApi.createProviderModel(sanitizedData);
    } catch (error) {
      console.error('Service error - createProviderModel:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Cập nhật provider model
   */
  static async updateProviderModel(
    id: string,
    data: UpdateProviderModelDto
  ): Promise<ApiResponseDto<ProviderModel>> {
    try {
      if (!id || id.trim() === '') {
        throw new Error('Provider model ID is required');
      }

      // Validate at least one field is provided
      if (!data.name && !data.apiKey) {
        throw new Error('At least one field must be provided for update');
      }

      // Sanitize data
      const sanitizedData: UpdateProviderModelDto = {};

      if (data.name !== undefined) {
        if (data.name.trim() === '') {
          throw new Error('Provider model name cannot be empty');
        }
        sanitizedData.name = data.name.trim();
      }

      if (data.apiKey !== undefined) {
        if (data.apiKey.trim() === '') {
          throw new Error('API key cannot be empty');
        }
        sanitizedData.apiKey = data.apiKey.trim();
      }

      return await ProviderModelApi.updateProviderModel(id.trim(), sanitizedData);
    } catch (error) {
      console.error(`Service error - updateProviderModel(${id}):`, error);
      throw this.handleError(error);
    }
  }

  /**
   * Xóa provider model
   */
  static async deleteProviderModel(id: string): Promise<ApiResponseDto<void>> {
    try {
      if (!id || id.trim() === '') {
        throw new Error('Provider model ID is required');
      }

      return await ProviderModelApi.deleteProviderModel(id.trim());
    } catch (error) {
      console.error(`Service error - deleteProviderModel(${id}):`, error);
      throw this.handleError(error);
    }
  }

  /**
   * Xóa nhiều provider models cùng lúc
   */
  static async deleteMultipleProviderModels(ids: string[]): Promise<ApiResponseDto<void>> {
    try {
      if (!ids || ids.length === 0) {
        throw new Error('At least one provider model ID is required');
      }

      // Validate and sanitize IDs
      const validIds = ids
        .map(id => id.trim())
        .filter(id => id !== '');

      if (validIds.length === 0) {
        throw new Error('No valid provider model IDs provided');
      }

      return await ProviderModelApi.deleteMultipleProviderModels(validIds);
    } catch (error) {
      console.error('Service error - deleteMultipleProviderModels:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Kiểm tra tính khả dụng của API key
   */
  static async validateApiKey(
    type: string,
    apiKey: string
  ): Promise<ApiResponseDto<{ valid: boolean; message?: string }>> {
    try {
      if (!type || type.trim() === '') {
        throw new Error('Provider type is required');
      }

      if (!apiKey || apiKey.trim() === '') {
        throw new Error('API key is required');
      }

      return await ProviderModelApi.validateApiKey(type.trim(), apiKey.trim());
    } catch (error) {
      console.error('Service error - validateApiKey:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Handle and format errors
   */
  private static handleError(error: unknown): Error {
    if (error instanceof Error) {
      return error;
    }

    // Handle API errors
    if (typeof error === 'object' && error !== null && 'response' in error) {
      const apiError = error as { response?: { data?: { message?: string } } };
      if (apiError.response?.data?.message) {
        return new Error(apiError.response.data.message);
      }
    }

    // Handle network errors
    if (typeof error === 'object' && error !== null && 'message' in error) {
      const networkError = error as { message: string };
      return new Error(networkError.message);
    }

    return new Error('An unexpected error occurred');
  }
}
