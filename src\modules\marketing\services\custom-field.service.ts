/**
 * Service for audience custom field API
 */

import { apiClient } from '@/shared/api';
import {
  CreateCustomFieldRequest,
  CustomFieldDetailResponse,
  CustomFieldListResponse,
  CustomFieldQueryParams as OriginalCustomFieldQueryParams,
  UpdateCustomFieldRequest,
} from '../types/custom-field.types';

// Additional types for business module compatibility
export interface CustomFieldListItem {
  id: number;
  configId: string;
  label: string;
  type: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CustomFieldDetail {
  id: number;
  configId: string;
  label: string;
  type: string;
  description?: string;
  configJson?: {
    id?: string;
    label?: string;
    displayName?: string;
    type?: string;
    placeholder?: string;
    defaultValue?: string;
    description?: string;
    validation?: {
      minLength?: number;
      maxLength?: number;
      pattern?: string;
    };
    options?: Array<{ label: string; value: string }>;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateCustomFieldData {
  component: string;
  config: {
    id: string;
    label: string;
    displayName: string;
    type: string;
    required?: boolean;
    placeholder?: string;
    defaultValue?: string;
    description?: string;
    validation?: {
      minLength?: number;
      maxLength?: number;
      pattern?: string;
    };
    options?: Array<{ label: string; value: string }>;
  };
}

export type UpdateCustomFieldData = CreateCustomFieldData;

export interface CustomFieldQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Base URL for audience custom field API
 */
const BASE_URL = '/user/marketing/audience-custom-fields';

/**
 * Custom field service
 */
export const CustomFieldService = {
  /**
   * Get custom fields with pagination and filtering
   */
  getCustomFields: async (params?: CustomFieldQueryParams): Promise<CustomFieldListResponse> => {
    try {
      // Chuẩn bị tham số truy vấn
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const queryParams: Record<string, any> = {
        page: params?.page || 1,
        limit: params?.limit || 10,
      };

      // Xử lý tham số tìm kiếm
      if (params?.search && params.search.trim()) {
        console.log('Search parameter detected:', params.search);
        queryParams.search = params.search.trim();
      }

      // Xử lý tham số sắp xếp
      if (params?.sortBy) {
        queryParams.sortBy = params.sortBy;
        if (params.sortDirection) {
          queryParams.sortDirection = params.sortDirection;
        }
      }

      // Log thông tin request
      console.log('Sending API request to:', BASE_URL);
      console.log('With params:', queryParams);

      // Gọi API
      const response = await apiClient.get<CustomFieldListResponse['result']>(BASE_URL, {
        params: queryParams,
        // Tăng timeout để tránh lỗi timeout
        timeout: 10000
      });

      console.log('API response received successfully');
      return response;
    } catch (error) {
      console.error('Error in getCustomFields:', error);
      throw error;
    }
  },

  /**
   * Get custom field by ID
   */
  getCustomFieldById: async (id: number): Promise<CustomFieldDetailResponse> => {
    return apiClient.get<CustomFieldDetailResponse['result']>(`${BASE_URL}/${id}`);
  },

  /**
   * Create custom field
   */
  createCustomField: async (data: CreateCustomFieldRequest): Promise<CustomFieldDetailResponse> => {
    return apiClient.post<CustomFieldDetailResponse['result']>(BASE_URL, data);
  },

  /**
   * Update custom field
   */
  updateCustomField: async (
    id: number,
    data: UpdateCustomFieldRequest
  ): Promise<CustomFieldDetailResponse> => {
    return apiClient.put<CustomFieldDetailResponse['result']>(`${BASE_URL}/${id}`, data);
  },

  /**
   * Delete custom field
   */
  deleteCustomField: async (id: number): Promise<CustomFieldDetailResponse> => {
    return apiClient.delete<CustomFieldDetailResponse['result']>(`${BASE_URL}/${id}`);
  },

  /**
   * Delete multiple custom fields
   */
  deleteMultipleCustomFields: async (customFieldIds: number[]): Promise<CustomFieldDetailResponse> => {
    return apiClient.delete<CustomFieldDetailResponse['result']>(`${BASE_URL}/bulk`, {
      data: { customFieldIds }
    });
  },
};
