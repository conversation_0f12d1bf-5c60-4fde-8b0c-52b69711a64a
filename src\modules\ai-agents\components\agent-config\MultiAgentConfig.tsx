import { Button, EmptyState, Icon } from '@/shared/components/common';
import React, { useState } from 'react';
import { MultiAgentConfigData, MultiAgentItem as MultiAgentItemType } from '../../types/agent';
import { TypeAgent } from '../agent-add/TypeAgentCard';
import AgentSelectForm from './AgentSelectForm';
import { ConfigComponentWrapper } from './ConfigComponentWrapper';
import MultiAgentItem from './MultiAgentItem';

interface MultiAgentConfigProps {
  /**
   * Dữ liệu cấu hình multi-agent ban đầu
   */
  initialData?: MultiAgentConfigData;

  /**
   * Callback khi lưu cấu hình
   */
  onSave?: (data: MultiAgentConfigData) => void;

  /**
   * Danh sách các type agent có thể chọn
   */
  availableAgents?: TypeAgent[];
}

/**
 * Component cấu hình multi-agent cho Agent
 */
const MultiAgentConfig: React.FC<MultiAgentConfigProps> = ({
  initialData,
  onSave,
  availableAgents = []
}) => {
  const [configData, setConfigData] = useState<MultiAgentConfigData>(
    initialData || { agents: [] }
  );
  const [showAddForm, setShowAddForm] = useState(false);

  // Xử lý thêm agent mới
  const handleAddAgent = (newAgent: MultiAgentItemType) => {
    const updatedData = {
      ...configData,
      agents: [...configData.agents, newAgent]
    };

    setConfigData(updatedData);
    setShowAddForm(false);

    if (onSave) {
      onSave(updatedData);
    }
  };

  // Xử lý cập nhật agent
  const handleUpdateAgent = (updatedAgent: MultiAgentItemType) => {
    const updatedData = {
      ...configData,
      agents: configData.agents.map(agent =>
        agent.id === updatedAgent.id ? updatedAgent : agent
      )
    };

    setConfigData(updatedData);

    if (onSave) {
      onSave(updatedData);
    }
  };

  // Xử lý xóa agent
  const handleRemoveAgent = (agentId: string) => {
    const updatedData = {
      ...configData,
      agents: configData.agents.filter(agent => agent.id !== agentId)
    };

    setConfigData(updatedData);

    if (onSave) {
      onSave(updatedData);
    }
  };

  // Xử lý di chuyển agent lên
  const handleMoveUp = (agentId: string) => {
    const currentIndex = configData.agents.findIndex(agent => agent.id === agentId);
    if (currentIndex <= 0) return;

    const newAgents = [...configData.agents];
    [newAgents[currentIndex - 1], newAgents[currentIndex]] =
      [newAgents[currentIndex], newAgents[currentIndex - 1]];

    const updatedData = {
      ...configData,
      agents: newAgents
    };

    setConfigData(updatedData);

    if (onSave) {
      onSave(updatedData);
    }
  };

  // Xử lý di chuyển agent xuống
  const handleMoveDown = (agentId: string) => {
    const currentIndex = configData.agents.findIndex(agent => agent.id === agentId);
    if (currentIndex >= configData.agents.length - 1) return;

    const newAgents = [...configData.agents];
    [newAgents[currentIndex], newAgents[currentIndex + 1]] =
      [newAgents[currentIndex + 1], newAgents[currentIndex]];

    const updatedData = {
      ...configData,
      agents: newAgents
    };

    setConfigData(updatedData);

    if (onSave) {
      onSave(updatedData);
    }
  };

  return (
    <ConfigComponentWrapper
      componentId="multiAgent"
      title={
        <div className="flex items-center">
          <span className="mr-2">
            <Icon name="users" size="sm" />
          </span>
          <span>Cấu hình Multi-Agent</span>
        </div>
      }
    >
      <div className="p-4 space-y-4">
        {/* Mô tả */}
        <div className="text-sm text-gray-600 dark:text-gray-300 mb-4">
          Cấu hình nhiều agent để làm việc cùng nhau. Mỗi agent sẽ có vai trò và chức năng riêng biệt.
        </div>

        {/* Form thêm agent mới */}
        <AgentSelectForm
          availableAgents={availableAgents}
          onAddAgent={handleAddAgent}
          onCancel={() => setShowAddForm(false)}
          isVisible={showAddForm}
        />

        {/* Nút thêm agent */}
        {!showAddForm && (
          <Button
            variant="outline"
            onClick={() => setShowAddForm(true)}
            leftIcon={<Icon name="plus" size="sm" />}
            className="w-full"
          >
            Thêm Agent
          </Button>
        )}

        {/* Danh sách agents */}
        {configData.agents.length > 0 ? (
          <div className="space-y-3">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Danh sách Agents ({configData.agents.length})
            </div>
            {configData.agents.map((agent, index) => (
              <MultiAgentItem
                key={agent.id}
                agent={agent}
                onUpdate={handleUpdateAgent}
                onRemove={handleRemoveAgent}
                canMoveUp={index > 0}
                canMoveDown={index < configData.agents.length - 1}
                onMoveUp={handleMoveUp}
                onMoveDown={handleMoveDown}
              />
            ))}
          </div>
        ) : (
          !showAddForm && (
            <EmptyState
              icon="users"
              title="Chưa có agent nào"
              description="Thêm agent đầu tiên để bắt đầu cấu hình multi-agent"
              className="py-8"
            />
          )
        )}
      </div>
    </ConfigComponentWrapper>
  );
};

export default MultiAgentConfig;
