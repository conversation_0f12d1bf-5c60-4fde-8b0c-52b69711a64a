import React, { useEffect, useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import { Mail, Edit, Send, FileText } from 'lucide-react';
import {
  Card,
  Table,
  Chip,
  ActionMenu,
  ActionMenuItem,
  Typography,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import ConfirmDeleteModal from '@/shared/components/common/ConfirmDeleteModal';
import { NotificationUtil } from '@/shared/utils/notification';
import {
  useEmailTemplatesAdapter,
  useEmailTemplateOverviewAdapter,
  useBulkDeleteEmailTemplatesAdapter,
} from '../../hooks/email/useEmailTemplatesAdapter';
import { CreateEmailTemplateForm } from '../../components/email/CreateEmailTemplateForm';
import { EditEmailTemplateForm } from '../../components/email/EditEmailTemplateForm';
import type {
  EmailTemplateQueryDto,
  EmailTemplateDto,
  EmailTemplateStatus,
} from '../../types/email.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { TableColumn } from '@/shared/components/common/Table/types';
import { formatTimestamp } from '@/shared/utils/date';

/**
 * Trang quản lý Email Templates
 */
export function EmailTemplatesPage() {
  const { t } = useTranslation('marketing');
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplateDto | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // Sử dụng hook animation cho form
  const {
    isVisible: isCreateVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();
  const {
    isVisible: isEditVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();
  const {
    showForm: showPreviewForm,
  } = useSlideForm();

  // Mutations
  const bulkDeleteMutation = useBulkDeleteEmailTemplatesAdapter();

  // Kiểm tra nếu có action=create trong URL thì hiển thị form
  useEffect(() => {
    if (searchParams.get('action') === 'create') {
      showCreateForm();
      setSearchParams({});
    }
  }, [searchParams, showCreateForm, setSearchParams]);

  const handlePreviewTemplate = useCallback(
    (template: EmailTemplateDto) => {
      setSelectedTemplate(template);
      showPreviewForm();
    },
    [showPreviewForm]
  );

  const handleEditTemplate = useCallback(
    (template: EmailTemplateDto) => {
      setSelectedTemplate(template);
      showEditForm();
    },
    [showEditForm]
  );

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      NotificationUtil.warning({
        message: t('marketing:email.templates.selectToDelete', 'Vui lòng chọn template để xóa'),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      await bulkDeleteMutation.mutateAsync(selectedRowKeys as string[]);
      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);
    } catch (error) {
      console.error('Error bulk deleting templates:', error);
    }
  }, [selectedRowKeys, bulkDeleteMutation]);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<EmailTemplateDto>[]>(
    () => [
      {
        key: 'template',
        title: t('marketing:email.templates.table.template', 'Template'),
        dataIndex: 'name',
        sortable: true,
        render: (value: unknown, record: EmailTemplateDto) => (
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 rounded bg-gradient-to-r from-orange-500 to-red-600 flex items-center justify-center text-white">
              <Mail className="h-6 w-6" />
            </div>
            <div>
              <Typography variant="body2" className="font-medium">
                {String(value || '')}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {record.subject}
              </Typography>
            </div>
          </div>
        ),
      },
      {
        key: 'type',
        title: t('marketing:email.templates.table.type', 'Loại'),
        dataIndex: 'type',
        sortable: true,
        render: (value: unknown) => {
          const type = value as string;
          const typeVariants: Record<
            string,
            'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default'
          > = {
            NEWSLETTER: 'info',
            PROMOTIONAL: 'warning',
            TRANSACTIONAL: 'success',
            WELCOME: 'primary',
            ABANDONED_CART: 'danger',
            FOLLOW_UP: 'default',
          };
          return (
            <Chip variant={typeVariants[type] || 'default'} size="sm">
              {type}
            </Chip>
          );
        },
      },
      {
        key: 'status',
        title: t('marketing:email.templates.table.status', 'Trạng thái'),
        dataIndex: 'status',
        sortable: true,
        render: (value: unknown) => {
          const status = value as string;
          switch (status) {
            case 'ACTIVE':
              return (
                <Chip variant="success" size="sm">
                  {t('marketing:email.templates.status.active', 'Hoạt động')}
                </Chip>
              );
            case 'DRAFT':
              return (
                <Chip variant="warning" size="sm">
                  {t('marketing:email.templates.status.draft', 'Bản nháp')}
                </Chip>
              );
            case 'ARCHIVED':
              return (
                <Chip variant="info" size="sm">
                  {t('marketing:email.templates.status.archived', 'Đã lưu trữ')}
                </Chip>
              );
            default:
              return (
                <Chip variant="info" size="sm">
                  {status}
                </Chip>
              );
          }
        },
      },
      {
        key: 'tags',
        title: t('marketing:email.templates.table.tags', 'Tags'),
        dataIndex: 'tags',
        render: (value: unknown) => {
          const tags = value as string[];
          return (
            <div className="flex flex-wrap gap-1">
              {tags?.slice(0, 2).map((tag: string) => (
                <Chip key={tag} variant="info" size="sm">
                  {tag}
                </Chip>
              ))}
              {tags?.length > 2 && (
                <Chip variant="info" size="sm">
                  +{tags.length - 2}
                </Chip>
              )}
            </div>
          );
        },
      },
      {
        key: 'variables',
        title: t('marketing:email.templates.table.variables', 'Biến'),
        dataIndex: 'variables',
        render: (value: unknown) => {
          const variables = value as Array<{ name: string; type: string; required: boolean }>;
          return <Typography variant="caption">{variables?.length || 0} biến</Typography>;
        },
      },
      {
        key: 'updatedAt',
        title: t('marketing:email.templates.table.updated', 'Cập nhật'),
        dataIndex: 'updatedAt',
        sortable: true,
        render: (value: unknown) => (
          <Typography variant="caption" className="text-muted-foreground">
            {formatTimestamp(value, 'vi-VN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
            })}
          </Typography>
        ),
      },
      {
        key: 'actions',
        title: t('marketing:email.templates.table.actions', 'Thao tác'),
        width: '120px',
        render: (_: unknown, record: EmailTemplateDto) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'preview',
              label: t('marketing:email.templates.preview.title', 'Xem trước Template'),
              icon: 'eye',
              onClick: () => handlePreviewTemplate(record),
            },
            {
              id: 'edit',
              label: t('common.edit', 'Chỉnh sửa'),
              icon: 'edit',
              onClick: () => handleEditTemplate(record),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="200px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handlePreviewTemplate, handleEditTemplate]
  );

  // Tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common.all', 'Tất cả'), icon: 'list', value: 'all' },
      {
        id: 'active',
        label: t('marketing:email.templates.status.active', 'Hoạt động'),
        icon: 'check',
        value: 'ACTIVE',
      },
      {
        id: 'draft',
        label: t('marketing:email.templates.status.draft', 'Bản nháp'),
        icon: 'edit',
        value: 'DRAFT',
      },
      {
        id: 'archived',
        label: t('marketing:email.templates.status.archived', 'Đã lưu trữ'),
        icon: 'archive',
        value: 'ARCHIVED',
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): EmailTemplateQueryDto => {
      const queryParams: EmailTemplateQueryDto = {
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection: params.sortDirection || undefined,
      };

      if (params.filterValue !== 'all') {
        queryParams.status = params.filterValue as EmailTemplateStatus;
      }

      return queryParams;
    },
    []
  );

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<EmailTemplateDto, EmailTemplateQueryDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // API calls
  const { data: templatesData, isLoading } = useEmailTemplatesAdapter(dataTable.queryParams);
  const { data: overviewData, isLoading: isOverviewLoading } = useEmailTemplateOverviewAdapter();

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {
        ACTIVE: t('marketing:email.templates.status.active', 'Hoạt động'),
        DRAFT: t('marketing:email.templates.status.draft', 'Bản nháp'),
        ARCHIVED: t('marketing:email.templates.status.archived', 'Đã lưu trữ'),
      },
      t,
    });

  const handleCreateSuccess = () => {
    hideCreateForm();
    setSearchParams({});
  };

  const handleEditSuccess = () => {
    hideEditForm();
    setSelectedTemplate(null);
  };

  const handleEditCancel = () => {
    hideEditForm();
    setSelectedTemplate(null);
  };

  return (
    <div className="w-full bg-background text-foreground space-y-4">
      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="caption" className="font-medium text-muted-foreground">
              {t('marketing:email.templates.stats.totalTemplates', 'Tổng Templates')}
            </Typography>
            <FileText className="h-4 w-4 text-blue-600" />
          </div>
          <Typography variant="h2" className="text-blue-600">
            {isOverviewLoading
              ? '...'
              : overviewData?.totalTemplates || templatesData?.meta.totalItems || 0}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground mt-1">
            {isOverviewLoading
              ? '...'
              : `+${overviewData?.newTemplatesThisWeek || 0} template mới tuần này`}
          </Typography>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="caption" className="font-medium text-muted-foreground">
              {t('marketing:email.templates.stats.active', 'Hoạt động')}
            </Typography>
            <Mail className="h-4 w-4 text-green-600" />
          </div>
          <Typography variant="h2" className="text-green-600">
            {isOverviewLoading
              ? '...'
              : overviewData?.activeTemplates ||
                templatesData?.items.filter(
                  (template: EmailTemplateDto) => template.status === 'ACTIVE'
                ).length ||
                0}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground mt-1">
            {t('marketing:email.templates.stats.readyToUse', 'Sẵn sàng sử dụng')}
          </Typography>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="caption" className="font-medium text-muted-foreground">
              {t('marketing:email.templates.stats.draft', 'Bản nháp')}
            </Typography>
            <Edit className="h-4 w-4 text-yellow-600" />
          </div>
          <Typography variant="h2" className="text-yellow-600">
            {isOverviewLoading
              ? '...'
              : overviewData?.draftTemplates ||
                templatesData?.items.filter(
                  (template: EmailTemplateDto) => template.status === 'DRAFT'
                ).length ||
                0}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground mt-1">
            {t('marketing:email.templates.stats.incomplete', 'Chưa hoàn thành')}
          </Typography>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="caption" className="font-medium text-muted-foreground">
              {t('marketing:email.templates.stats.testSent', 'Đã gửi test')}
            </Typography>
            <Send className="h-4 w-4 text-orange-600" />
          </div>
          <Typography variant="h2" className="text-orange-600">
            {isOverviewLoading ? '...' : overviewData?.testSentThisWeek || 0}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground mt-1">
            {t('marketing:email.templates.stats.thisWeek', 'Tuần này')}
          </Typography>
        </Card>
      </div>

      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={() => showCreateForm()}
        items={dataTable.menuItems}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete', 'Xóa nhiều'),
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* SlideInForm cho tạo template */}
      <SlideInForm isVisible={isCreateVisible}>
        <div className="space-y-4">
          <div>
            <Typography variant="h3">
              {t('marketing:email.templates.create.title', 'Tạo Email Template')}
            </Typography>
          </div>
          <CreateEmailTemplateForm onSuccess={handleCreateSuccess} onCancel={hideCreateForm} />
        </div>
      </SlideInForm>

      {/* SlideInForm cho chỉnh sửa template */}
      <SlideInForm isVisible={isEditVisible}>
        {selectedTemplate && (
          <EditEmailTemplateForm
            templateId={selectedTemplate.id}
            onSuccess={handleEditSuccess}
            onCancel={handleEditCancel}
          />
        )}
      </SlideInForm>

      {/* Templates Table */}
      <Card className="overflow-hidden">
        <Table<EmailTemplateDto>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={templatesData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: templatesData?.meta.currentPage || 1,
            pageSize: templatesData?.meta.itemsPerPage || 10,
            total: templatesData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t('marketing:email.templates.confirmBulkDeleteMessage', 'Bạn có chắc chắn muốn xóa {{count}} template đã chọn?', { count: selectedRowKeys.length })}
        isLoading={bulkDeleteMutation.isPending}
      />
    </div>
  );
}

export default EmailTemplatesPage;
