import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useWatch } from 'react-hook-form';
import {
  Card,
  Typography,
  Button,
  Icon,
  Form,
  FormItem,
  Input,
  Select,
  Switch,
  Textarea,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import { cloudStorageProviderConfigurationSchema } from '../schemas';
import { CLOUD_STORAGE_PROVIDER_TYPES } from '../constants';
import { useCreateCloudStorageProvider, useUpdateCloudStorageProvider, useTestCloudStorageProviderWithConfig } from '../hooks';
import type { CloudStorageProviderConfiguration, CloudStorageProviderFormData, CloudStorageProviderType } from '../types';

/**
 * Component hiển thị thông tin provider được chọn
 */
const ProviderInfo: React.FC = () => {
  const { t } = useTranslation(['integration']);
  const providerType = useWatch<CloudStorageProviderFormData, 'providerType'>({ name: 'providerType' });

  const selectedProvider = providerType ? CLOUD_STORAGE_PROVIDER_TYPES[providerType as CloudStorageProviderType] : null;

  if (!selectedProvider) return null;

  return (
    <Card className="p-4 bg-muted/50">
      <div className="flex items-start gap-3">
        <Icon name="info" size="sm" className="text-primary mt-1" />
        <div className="space-y-2">
          <Typography variant="subtitle2">
            {selectedProvider.displayName}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            {selectedProvider.description}
          </Typography>
          <div className="flex flex-wrap gap-2 mt-2">
            <Typography variant="caption" className="text-muted-foreground">
              <strong>{t('integration:cloudStorage.details.storageQuota')}:</strong> {selectedProvider.maxFileSize}
            </Typography>
          </div>
          <div className="flex flex-wrap gap-1 mt-2">
            {selectedProvider.supportedFeatures.map((feature: string) => (
              <span
                key={feature}
                className="px-2 py-1 bg-primary/10 text-primary text-xs rounded"
              >
                {feature}
              </span>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
};

interface CloudStorageProviderFormProps {
  provider?: CloudStorageProviderConfiguration;
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * Form tạo/chỉnh sửa Cloud Storage Provider
 */
const CloudStorageProviderForm: React.FC<CloudStorageProviderFormProps> = ({
  provider,
  onSuccess,
  onCancel
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const { formRef, setFormErrors } = useFormErrors<CloudStorageProviderFormData>();

  const defaultValues: CloudStorageProviderFormData = {
    providerType: provider?.providerType || 'google-drive',
    providerName: provider?.providerName || '',
    clientId: provider?.clientId || '',
    clientSecret: provider?.clientSecret || '',
    refreshToken: provider?.refreshToken || '',
    rootFolderId: provider?.rootFolderId || '',
    isActive: provider?.isActive ?? true,
    autoSync: provider?.autoSync ?? false,
    syncFolders: provider?.syncFolders ? JSON.stringify(provider.syncFolders) : '',
  };

  const [isTestingConnection, setIsTestingConnection] = useState(false);

  const createMutation = useCreateCloudStorageProvider();
  const updateMutation = useUpdateCloudStorageProvider();
  const testMutation = useTestCloudStorageProviderWithConfig();

  const isEditing = !!provider;
  const isLoading = createMutation.isPending || updateMutation.isPending;

  // Provider options
  const providerOptions = Object.values(CLOUD_STORAGE_PROVIDER_TYPES).map(provider => ({
    value: provider.id,
    label: provider.displayName,
  }));

  const handleTestConnection = async () => {
    try {
      setIsTestingConnection(true);

      // Get current form values
      const formValues = formRef.current?.getValues();
      if (!formValues) return;

      // Validate form data first
      const validatedData = cloudStorageProviderConfigurationSchema.parse(formValues);

      // Parse sync folders
      if (validatedData.syncFolders) {
        try {
          JSON.parse(validatedData.syncFolders);
        } catch {
          setFormErrors({ syncFolders: t('integration:cloudStorage.validation.syncFolders.invalidJson') });
          return;
        }
      }

      const testData = {
        storageConfig: {
          providerType: validatedData.providerType,
          providerName: validatedData.providerName,
          clientId: validatedData.clientId,
          clientSecret: validatedData.clientSecret,
          refreshToken: validatedData.refreshToken,
          rootFolderId: validatedData.rootFolderId,
        },
        testInfo: {
          testFolderName: 'RedAI Test Folder',
          testFileName: 'test-connection.txt',
        },
      };

      const result = await testMutation.mutateAsync(testData);

      if (result.result.result.success) {
        // TODO: Show success toast
        console.log('Test connection successful:', result.result.result);
      } else {
        // TODO: Show error toast
        console.error('Test connection failed:', result.result.result.message);
      }
    } catch (error: unknown) {
      console.error('Test connection error:', error);
      if (error && typeof error === 'object' && 'issues' in error) {
        // Zod validation errors
        const fieldErrors: Partial<Record<keyof CloudStorageProviderFormData, string>> = {};
        const zodError = error as { issues: Array<{ path: string[]; message: string }> };
        zodError.issues.forEach((issue) => {
          const field = issue.path[0] as keyof CloudStorageProviderFormData;
          fieldErrors[field] = issue.message;
        });
        setFormErrors(fieldErrors);
      }
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleSubmit = async (values: CloudStorageProviderFormData) => {
    try {
      // Parse sync folders
      let syncFolders: string[] = [];
      if (values.syncFolders) {
        try {
          syncFolders = JSON.parse(values.syncFolders);
        } catch {
          setFormErrors({ syncFolders: t('integration:cloudStorage.validation.syncFolders.invalidJson') });
          return;
        }
      }

      const submitData = {
        providerType: values.providerType,
        providerName: values.providerName,
        clientId: values.clientId,
        clientSecret: values.clientSecret,
        refreshToken: values.refreshToken,
        rootFolderId: values.rootFolderId,
        isActive: values.isActive,
        autoSync: values.autoSync,
        syncFolders,
      };

      if (isEditing && provider) {
        await updateMutation.mutateAsync({ id: provider.id, data: submitData });
      } else {
        await createMutation.mutateAsync(submitData);
      }

      onSuccess?.();
    } catch (error: unknown) {
      console.error('Form submission error:', error);
      if (error && typeof error === 'object' && 'issues' in error) {
        // Zod validation errors
        const fieldErrors: Partial<Record<keyof CloudStorageProviderFormData, string>> = {};
        const zodError = error as { issues: Array<{ path: string[]; message: string }> };
        zodError.issues.forEach((issue) => {
          const field = issue.path[0] as keyof CloudStorageProviderFormData;
          fieldErrors[field] = issue.message;
        });
        setFormErrors(fieldErrors);
      }
    }
  };

  return (
    <Card className="w-full">
      <div className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <Icon name="cloud" size="lg" className="text-primary" />
          <Typography variant="h3">
            {isEditing
              ? t('integration:cloudStorage.form.editTitle')
              : t('integration:cloudStorage.form.createTitle')
            }
          </Typography>
        </div>

        <Form
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          ref={formRef as any}
          schema={cloudStorageProviderConfigurationSchema}
          defaultValues={defaultValues}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onSubmit={handleSubmit as any}
          submitOnEnter={false}
          className="space-y-6"
        >
          {/* Provider Type */}
          <div className="space-y-1">
            <FormItem
              label={t('integration:cloudStorage.form.providerType.label')}
              name="providerType"
              required
            >
              <Select
                options={providerOptions}
                placeholder={t('integration:cloudStorage.form.providerType.placeholder')}
                fullWidth
                disabled={isEditing} // Cannot change provider type when editing
              />
            </FormItem>
            <Typography variant="caption" className="text-muted-foreground">
              {t('integration:cloudStorage.form.providerType.helpText')}
            </Typography>
          </div>

          {/* Provider Name */}
          <div className="space-y-1">
            <FormItem
              label={t('integration:cloudStorage.form.providerName.label')}
              name="providerName"
              required
            >
              <Input
                type="text"
                placeholder={t('integration:cloudStorage.form.providerName.placeholder')}
                fullWidth
              />
            </FormItem>
            <Typography variant="caption" className="text-muted-foreground">
              {t('integration:cloudStorage.form.providerName.helpText')}
            </Typography>
          </div>

          {/* Client ID */}
          <div className="space-y-1">
            <FormItem
              label={t('integration:cloudStorage.form.clientId.label')}
              name="clientId"
              required
            >
              <Input
                type="text"
                placeholder={t('integration:cloudStorage.form.clientId.placeholder')}
                fullWidth
              />
            </FormItem>
            <Typography variant="caption" className="text-muted-foreground">
              {t('integration:cloudStorage.form.clientId.helpText')}
            </Typography>
          </div>

          {/* Client Secret */}
          <div className="space-y-1">
            <FormItem
              label={t('integration:cloudStorage.form.clientSecret.label')}
              name="clientSecret"
              required
            >
              <Input
                type="password"
                placeholder={t('integration:cloudStorage.form.clientSecret.placeholder')}
                fullWidth
              />
            </FormItem>
            <Typography variant="caption" className="text-muted-foreground">
              {t('integration:cloudStorage.form.clientSecret.helpText')}
            </Typography>
          </div>

          {/* Refresh Token */}
          <div className="space-y-1">
            <FormItem
              label={t('integration:cloudStorage.form.refreshToken.label')}
              name="refreshToken"
              required
            >
              <Textarea
                placeholder={t('integration:cloudStorage.form.refreshToken.placeholder')}
                rows={3}
                fullWidth
              />
            </FormItem>
            <Typography variant="caption" className="text-muted-foreground">
              {t('integration:cloudStorage.form.refreshToken.helpText')}
            </Typography>
          </div>

          {/* Root Folder ID */}
          <div className="space-y-1">
            <FormItem
              label={t('integration:cloudStorage.form.rootFolderId.label')}
              name="rootFolderId"
            >
              <Input
                type="text"
                placeholder={t('integration:cloudStorage.form.rootFolderId.placeholder')}
                fullWidth
              />
            </FormItem>
            <Typography variant="caption" className="text-muted-foreground">
              {t('integration:cloudStorage.form.rootFolderId.helpText')}
            </Typography>
          </div>

          {/* Sync Folders */}
          <div className="space-y-1">
            <FormItem
              label={t('integration:cloudStorage.form.syncFolders.label')}
              name="syncFolders"
            >
              <Textarea
                placeholder={t('integration:cloudStorage.form.syncFolders.placeholder')}
                rows={3}
                fullWidth
              />
            </FormItem>
            <Typography variant="caption" className="text-muted-foreground">
              {t('integration:cloudStorage.form.syncFolders.helpText')}
            </Typography>
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <Typography variant="h6">
              {t('common:settings')}
            </Typography>

            {/* Is Active */}
            <div className="space-y-1">
              <FormItem
                label={t('integration:cloudStorage.form.isActive.label')}
                name="isActive"
              >
                <Switch />
              </FormItem>
              <Typography variant="caption" className="text-muted-foreground">
                {t('integration:cloudStorage.form.isActive.helpText')}
              </Typography>
            </div>

            {/* Auto Sync */}
            <div className="space-y-1">
              <FormItem
                label={t('integration:cloudStorage.form.autoSync.label')}
                name="autoSync"
              >
                <Switch />
              </FormItem>
              <Typography variant="caption" className="text-muted-foreground">
                {t('integration:cloudStorage.form.autoSync.helpText')}
              </Typography>
            </div>
          </div>

          {/* Provider Info */}
          <ProviderInfo />

          {/* Actions */}
          <div className="flex gap-3 pt-6 border-t">
            {/* Test Connection */}
            <Button
              type="button"
              variant="outline"
              onClick={handleTestConnection}
              disabled={isTestingConnection || isLoading}
              className="flex-1"
            >
              {isTestingConnection ? (
                <>
                  <Icon name="loading" size="sm" className="mr-2" />
                  {t('common:testing')}
                </>
              ) : (
                <>
                  <Icon name="zap" size="sm" className="mr-2" />
                  {t('integration:cloudStorage.form.testConnection')}
                </>
              )}
            </Button>

            {/* Cancel */}
            {onCancel && (
              <Button
                type="button"
                variant="ghost"
                onClick={onCancel}
                disabled={isLoading}
                className="flex-1"
              >
                {t('common:cancel')}
              </Button>
            )}

            {/* Submit */}
            <Button
              type="submit"
              variant="primary"
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Icon name="loading" size="sm" className="mr-2" />
                  {isEditing ? t('common:updating') : t('common:creating')}
                </>
              ) : (
                <>
                  <Icon name="save" size="sm" className="mr-2" />
                  {isEditing ? t('common:update') : t('common:create')}
                </>
              )}
            </Button>
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default CloudStorageProviderForm;
