import React, { useRef } from 'react';
import { Icon, Avatar } from '@/shared/components/common';
import { IconName } from '@/shared/components/common/Icon';
import { CSSTransition } from 'react-transition-group';

// Định nghĩa các constants
export type ChipVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info' | 'default';
export type ChipSize = 'sm' | 'md' | 'lg';

export interface ChipProps {
  /**
   * Nội dung hiển thị trong chip
   */
  children: React.ReactNode;

  /**
   * Variant của chip
   */
  variant?: ChipVariant;

  /**
   * Kích thước của chip
   */
  size?: ChipSize;

  /**
   * Icon hiển thị bên trái
   */
  leftIcon?: React.ReactNode;

  /**
   * Tên icon hiển thị bên trái (thay thế cho leftIcon)
   */
  leftIconName?: IconName;

  /**
   * Icon hiển thị bên phải
   */
  rightIcon?: React.ReactNode;

  /**
   * Tên icon hiển thị bên phải (thay thế cho rightIcon)
   */
  rightIconName?: IconName;

  /**
   * Hiển thị nút xóa
   */
  closable?: boolean;

  /**
   * Hàm xử lý khi click vào nút xóa
   */
  onClose?: (e: React.MouseEvent<HTMLButtonElement>) => void;

  /**
   * Hàm xử lý khi click vào chip
   */
  onClick?: (e: React.MouseEvent<HTMLDivElement>) => void;

  /**
   * Trạng thái disabled
   */
  disabled?: boolean;

  /**
   * Hiển thị dưới dạng outline
   */
  outlined?: boolean;

  /**
   * Avatar hiển thị trong chip
   */
  avatar?: React.ReactNode;

  /**
   * URL avatar (thay thế cho avatar)
   */
  avatarSrc?: string;

  /**
   * Trạng thái loading
   */
  loading?: boolean;

  /**
   * Trạng thái selected (cho multi-select)
   */
  isSelected?: boolean;

  /**
   * Hàm xử lý khi select/deselect chip
   */
  onSelect?: (selected: boolean) => void;

  /**
   * ID duy nhất cho chip (sử dụng trong ChipGroup)
   */
  id?: string | number;

  /**
   * Hiệu ứng khi xuất hiện/biến mất
   */
  animation?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Chip component hiển thị các thông tin nhỏ gọn, có thể chọn/bỏ chọn
 *
 * @example
 * // Chip cơ bản
 * <Chip>Tag</Chip>
 *
 * @example
 * // Chip với variant và icon
 * <Chip variant="primary" leftIconName="user">
 *   User
 * </Chip>
 *
 * @example
 * // Chip có thể xóa
 * <Chip closable onClose={() => console.log('Chip closed')}>
 *   Closable
 * </Chip>
 *
 * @example
 * // Chip có thể click
 * <Chip onClick={() => console.log('Chip clicked')}>
 *   Clickable
 * </Chip>
 *
 * @example
 * // Chip với avatar
 * <Chip avatarSrc="/path/to/avatar.jpg">
 *   User with Avatar
 * </Chip>
 *
 * @example
 * // Chip với loading state
 * <Chip loading>
 *   Loading
 * </Chip>
 */
const Chip = React.memo<ChipProps>(
  ({
    children,
    variant = 'default',
    size = 'md',
    leftIcon,
    leftIconName,
    rightIcon,
    rightIconName,
    closable = false,
    onClose,
    onClick,
    disabled = false,
    outlined = false,
    avatar,
    avatarSrc,
    loading = false,
    isSelected = false,
    onSelect,
    id,
    animation = false,
    className = '',
  }) => {
    const nodeRef = useRef<HTMLDivElement>(null);

    // Variant classes
    const variantClasses = {
      default: outlined
        ? 'bg-transparent border border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-300'
        : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
      primary: outlined
        ? 'bg-transparent border border-primary text-primary'
        : 'bg-primary text-white',
      secondary: outlined
        ? 'bg-transparent border border-gray-400 text-gray-600 dark:border-gray-500 dark:text-gray-400'
        : 'bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200',
      success: outlined
        ? 'bg-transparent border border-green-500 text-green-600 dark:text-green-400'
        : 'bg-green-500 text-white',
      warning: outlined
        ? 'bg-transparent border border-yellow-500 text-yellow-600 dark:text-yellow-400'
        : 'bg-yellow-500 text-white',
      danger: outlined
        ? 'bg-transparent border border-red-500 text-red-600 dark:text-red-400'
        : 'bg-red-500 text-white',
      info: outlined
        ? 'bg-transparent border border-blue-500 text-blue-600 dark:text-blue-400'
        : 'bg-blue-500 text-white',
    };

    // Size classes
    const sizeClasses = {
      sm: 'text-xs px-2 py-0.5 h-6',
      md: 'text-sm px-2.5 py-1 h-8',
      lg: 'text-base px-3 py-1.5 h-10',
    };

    // Icon size based on chip size
    const iconSize = {
      sm: 'xs',
      md: 'sm',
      lg: 'md',
    }[size] as 'xs' | 'sm' | 'md';

    // Close button size classes
    const closeButtonClasses = {
      sm: 'w-4 h-4 ml-1',
      md: 'w-5 h-5 ml-1.5',
      lg: 'w-6 h-6 ml-2',
    };

    // Avatar size based on chip size - adjusted to be slightly smaller than chip
    const avatarSize = {
      sm: 'xs', // 24px chip -> 16px avatar (xs)
      md: 'xs', // 32px chip -> 16px avatar (xs) - was 'sm' (24px)
      lg: 'sm', // 40px chip -> 24px avatar (sm) - was 'md' (32px)
    }[size] as 'xs' | 'sm' | 'md';

    // Hover effect classes
    const hoverClasses =
      (onClick || onSelect) && !disabled
        ? 'cursor-pointer hover:opacity-80 focus:ring-2 focus:ring-offset-2 focus:ring-primary/50'
        : '';

    // Disabled classes
    const disabledClasses = disabled ? 'opacity-60 cursor-not-allowed pointer-events-none' : '';

    // Loading classes
    const loadingClasses = loading ? 'relative overflow-hidden' : '';

    // Selected classes
    const selectedClasses = isSelected
      ? 'ring-2 ring-primary ring-offset-1 dark:ring-offset-gray-800'
      : '';

    // Combine all classes
    const chipClasses = [
      'inline-flex items-center justify-center rounded-full transition-all',
      variantClasses[variant],
      sizeClasses[size],
      hoverClasses,
      disabledClasses,
      loadingClasses,
      selectedClasses,
      className,
    ].join(' ');

    // Handle close button click
    const handleClose = (e: React.MouseEvent<HTMLButtonElement>) => {
      e.stopPropagation();
      if (onClose && !disabled && !loading) {
        onClose(e);
      }
    };

    // Handle chip click
    const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
      if (disabled || loading) return;

      if (onClick) {
        onClick(e);
      }

      if (onSelect) {
        onSelect(!isSelected);
      }
    };

    // Handle keyboard navigation
    const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
      if ((e.key === 'Enter' || e.key === ' ') && !disabled && !loading) {
        e.preventDefault();

        if (onClick) {
          onClick(e as unknown as React.MouseEvent<HTMLDivElement>);
        }

        if (onSelect) {
          onSelect(!isSelected);
        }
      }
    };

    // Render avatar
    const renderAvatar = () => {
      // Avatar margin based on size
      const marginClass = {
        sm: 'mr-1',
        md: 'mr-1.5',
        lg: 'mr-2',
      }[size];

      if (avatar) {
        return <span className={`flex items-center ${marginClass}`}>{avatar}</span>;
      }

      if (avatarSrc) {
        // Negative margin to compensate for avatar container padding
        const negativeMarginClass = {
          sm: '-ml-0.5 -my-0.5',
          md: '-ml-1 -my-0.5',
          lg: '-ml-1 -my-1',
        }[size];

        return (
          <span className={`flex items-center ${marginClass} ${negativeMarginClass}`}>
            <Avatar src={avatarSrc} alt="Avatar" size={avatarSize} />
          </span>
        );
      }

      return null;
    };

    // Render left icon
    const renderLeftIcon = () => {
      if (leftIcon) {
        return <span className="flex items-center mr-1.5">{leftIcon}</span>;
      }

      if (leftIconName) {
        return (
          <span className="flex items-center mr-1.5">
            <Icon name={leftIconName} size={iconSize} />
          </span>
        );
      }

      return null;
    };

    // Render right icon
    const renderRightIcon = () => {
      if (rightIcon) {
        return <span className="flex items-center ml-1.5">{rightIcon}</span>;
      }

      if (rightIconName) {
        return (
          <span className="flex items-center ml-1.5">
            <Icon name={rightIconName} size={iconSize} />
          </span>
        );
      }

      return null;
    };

    // Render close button
    const renderCloseButton = () => {
      if (!closable) return null;

      return (
        <button
          type="button"
          className={`${closeButtonClasses[size]} flex items-center justify-center ml-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10 transition-colors`}
          onClick={handleClose}
          aria-label="Close"
          disabled={disabled || loading}
        >
          <Icon name="close" size={iconSize} />
        </button>
      );
    };

    // Render loading indicator
    const renderLoading = () => {
      if (!loading) return null;

      return (
        <div className="absolute inset-0 flex items-center justify-center bg-white/30 dark:bg-black/30 rounded-full">
          <div className="animate-spin w-4 h-4 border-2 border-primary border-t-transparent rounded-full" />
        </div>
      );
    };

    const chipContent = (
      <div
        ref={nodeRef}
        className={chipClasses}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        role={onClick || onSelect ? 'button' : undefined}
        tabIndex={(onClick || onSelect) && !disabled && !loading ? 0 : undefined}
        aria-disabled={disabled || loading}
        aria-selected={onSelect ? isSelected : undefined}
        data-id={id}
      >
        {renderAvatar()}
        {renderLeftIcon()}
        <span className="flex items-center">{children}</span>
        {renderRightIcon()}
        {renderCloseButton()}
        {renderLoading()}
      </div>
    );

    // Apply animation if needed
    if (animation) {
      return (
        <CSSTransition
          in={true}
          appear={true}
          timeout={300}
          classNames={{
            appear: 'opacity-0 scale-90',
            appearActive: 'transition-all duration-300 opacity-100 scale-100',
            exit: 'opacity-100 scale-100',
            exitActive: 'transition-all duration-300 opacity-0 scale-90',
          }}
          nodeRef={nodeRef}
        >
          {chipContent}
        </CSSTransition>
      );
    }

    return chipContent;
  }
);

Chip.displayName = 'Chip';

export default Chip;
