/**
 * Hooks cho User Provider APIs
 * Tuân thủ quy tắc ProductGuide.md - Section 4: REACT HOOKS
 */

import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { getUserProviders } from '../api/agent.api';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';
import { UserProviderModelResponseDto } from '../types';

/**
 * Hook để lấy danh sách user providers
 * @param params Query params
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetUserProviders = (
  params?: QueryDto,
  options?: UseQueryOptions<ApiResponse<PaginatedResult<UserProviderModelResponseDto>>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.USER_PROVIDERS, params],
    queryFn: () => getUserProviders(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để lấy tất cả user providers (không phân trang)
 * @param options TanStack Query options
 * @returns Query result với tất cả providers
 */
export const useGetAllUserProviders = (
  options?: UseQueryOptions<ApiResponse<PaginatedResult<UserProviderModelResponseDto>>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.USER_PROVIDERS, 'all'],
    queryFn: () => getUserProviders({ page: 1, limit: 1000 }), // Lấy tất cả
    staleTime: 10 * 60 * 1000, // 10 minutes (providers ít thay đổi)
    select: (data) => {
      // Transform để trả về items array trong result
      return {
        ...data,
        result: {
          items: data.result?.items || [],
          meta: data.result?.meta || {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: 0,
            totalPages: 0,
            currentPage: 1
          }
        }
      };
    },
    ...options,
  });
};
