import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  FormItem,
  Input,
  Textarea,
  DateTimePicker,
} from '@/shared/components/common';

interface ServiceDeliveryInfo {
  serviceDate?: string;
  serviceTime?: string;
  serviceLocation?: string;
  serviceNotes?: string;
  contactPerson?: string;
  contactPhone?: string;
}

interface ServiceDeliveryFormProps {
  serviceInfo?: ServiceDeliveryInfo;
  onServiceInfoChange: (info: ServiceDeliveryInfo) => void;
  customerName?: string;
  customerPhone?: string;
}

/**
 * Form thông tin dịch vụ
 */
const ServiceDeliveryForm: React.FC<ServiceDeliveryFormProps> = ({
  serviceInfo,
  onServiceInfoChange,
  customerName,
  customerPhone,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Xử lý thay đổi ngày dịch vụ
  const handleServiceDateChange = (date: Date | null) => {
    onServiceInfoChange({
      ...serviceInfo,
      serviceDate: date ? date.toISOString() : undefined,
    });
  };

  // Xử lý thay đổi địa điểm dịch vụ
  const handleLocationChange = (serviceLocation: string) => {
    onServiceInfoChange({
      ...serviceInfo,
      serviceLocation,
    });
  };

  // Xử lý thay đổi ghi chú dịch vụ
  const handleNotesChange = (serviceNotes: string) => {
    onServiceInfoChange({
      ...serviceInfo,
      serviceNotes,
    });
  };

  // Xử lý thay đổi người liên hệ
  const handleContactPersonChange = (contactPerson: string) => {
    onServiceInfoChange({
      ...serviceInfo,
      contactPerson,
    });
  };

  // Xử lý thay đổi số điện thoại liên hệ
  const handleContactPhoneChange = (contactPhone: string) => {
    onServiceInfoChange({
      ...serviceInfo,
      contactPhone,
    });
  };

  return (
    <Card>
      <div className="p-6">
        <Typography variant="h6" className="mb-4">
          {t('business:order.serviceDelivery.title')}
        </Typography>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem label={t('business:order.serviceDelivery.serviceDate')}>
            <DateTimePicker
              value={serviceInfo?.serviceDate ? new Date(serviceInfo.serviceDate) : null}
              onChange={handleServiceDateChange}
              placeholder={t('business:order.serviceDelivery.serviceDatePlaceholder')}
              fullWidth
            />
          </FormItem>
          
          <FormItem label={t('business:order.serviceDelivery.serviceLocation')}>
            <Input
              value={serviceInfo?.serviceLocation || ''}
              onChange={(e) => handleLocationChange(e.target.value)}
              placeholder={t('business:order.serviceDelivery.serviceLocationPlaceholder')}
              fullWidth
            />
          </FormItem>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem label={t('business:order.serviceDelivery.contactPerson')}>
            <Input
              value={serviceInfo?.contactPerson || customerName || ''}
              onChange={(e) => handleContactPersonChange(e.target.value)}
              placeholder={t('business:order.serviceDelivery.contactPersonPlaceholder')}
              fullWidth
            />
          </FormItem>
          
          <FormItem label={t('business:order.serviceDelivery.contactPhone')}>
            <Input
              value={serviceInfo?.contactPhone || customerPhone || ''}
              onChange={(e) => handleContactPhoneChange(e.target.value)}
              placeholder={t('business:order.serviceDelivery.contactPhonePlaceholder')}
              fullWidth
            />
          </FormItem>
        </div>
        
        <FormItem label={t('business:order.serviceDelivery.serviceNotes')}>
          <Textarea
            value={serviceInfo?.serviceNotes || ''}
            onChange={(e) => handleNotesChange(e.target.value)}
            placeholder={t('business:order.serviceDelivery.serviceNotesPlaceholder')}
            rows={4}
            fullWidth
          />
        </FormItem>

        {/* Thông tin hướng dẫn */}
        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <Typography variant="subtitle2" className="mb-2 text-blue-800">
            {t('business:order.serviceDelivery.instructions')}
          </Typography>
          <Typography variant="body2" className="text-blue-600">
            {t('business:order.serviceDelivery.instructionsText')}
          </Typography>
        </div>
      </div>
    </Card>
  );
};

export default ServiceDeliveryForm;
