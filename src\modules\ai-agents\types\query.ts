/**
 * Query Types cho AI Agents module
 * File này chứa các interface cho query parameters
 */

/**
 * Interface cho query params của API lấy danh sách agents
 */
export interface GetAgentsQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: SortDirection;
  typeId?: number;
  active?: boolean;
}

/**
 * Enum cho hướng sắp xếp (khớp với backend)
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC'
}

/**
 * Enum cho các trường sắp xếp của type agent (khớp với backend)
 */
export enum TypeAgentSortBy {
  NAME = 'name',
  CREATED_AT = 'createdAt',
}

/**
 * Interface cho query params của API lấy danh sách type agents
 */
export interface GetTypeAgentsQueryDto {
  page?: number;
  limit?: number;
  isSystem?: boolean;
  search?: string;
  sortBy?: TypeAgentSortBy;
  sortDirection?: SortDirection;
}

/**
 * Interface cho query thống kê Agent
 */
export interface AgentStatisticsQueryDto {
  startDate?: string;
  endDate?: string;
  period?: 'day' | 'week' | 'month';
}
