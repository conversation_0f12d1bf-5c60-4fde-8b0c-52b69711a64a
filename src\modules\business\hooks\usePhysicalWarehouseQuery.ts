import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { PhysicalWarehouseService } from '../services/physical-warehouse.service';
import {
  CreatePhysicalWarehouseDto,
  UpdatePhysicalWarehouseDto,
  QueryPhysicalWarehouseDto,
} from '../types/physical-warehouse.types';

// Định nghĩa các query key
export const PHYSICAL_WAREHOUSE_QUERY_KEYS = {
  all: ['physical-warehouses'] as const,
  lists: () => [...PHYSICAL_WAREHOUSE_QUERY_KEYS.all, 'list'] as const,
  list: (filters: QueryPhysicalWarehouseDto) => [...PHYSICAL_WAREHOUSE_QUERY_KEYS.lists(), filters] as const,
  details: () => [...PHYSICAL_WAREHOUSE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...PHYSICAL_WAREHOUSE_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách kho vật lý
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const usePhysicalWarehouses = (params?: QueryPhysicalWarehouseDto) => {
  return useQuery({
    queryKey: PHYSICAL_WAREHOUSE_QUERY_KEYS.list(params || { page: 1, limit: 10 }),
    queryFn: () => PhysicalWarehouseService.getPhysicalWarehouses(params),
  });
};

/**
 * Hook để lấy chi tiết kho vật lý theo ID
 * @param id ID của kho vật lý
 * @returns Query object
 */
export const usePhysicalWarehouse = (id: number) => {
  return useQuery({
    queryKey: PHYSICAL_WAREHOUSE_QUERY_KEYS.detail(id),
    queryFn: () => PhysicalWarehouseService.getPhysicalWarehouseById(id),
    enabled: !!id,
  });
};

/**
 * Hook để tạo kho vật lý mới
 * @returns Mutation object
 */
export const useCreatePhysicalWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreatePhysicalWarehouseDto) => PhysicalWarehouseService.createPhysicalWarehouse(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: PHYSICAL_WAREHOUSE_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để cập nhật kho vật lý
 * @returns Mutation object
 */
export const useUpdatePhysicalWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdatePhysicalWarehouseDto }) =>
      PhysicalWarehouseService.updatePhysicalWarehouse(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: PHYSICAL_WAREHOUSE_QUERY_KEYS.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: PHYSICAL_WAREHOUSE_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để xóa kho vật lý
 * @returns Mutation object
 */
export const useDeletePhysicalWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => PhysicalWarehouseService.deletePhysicalWarehouse(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: PHYSICAL_WAREHOUSE_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để xóa nhiều kho vật lý
 * @returns Mutation object
 */
export const useDeleteMultiplePhysicalWarehouses = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (warehouseIds: number[]) => PhysicalWarehouseService.deleteMultiplePhysicalWarehouses(warehouseIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: PHYSICAL_WAREHOUSE_QUERY_KEYS.lists() });
    },
  });
};
