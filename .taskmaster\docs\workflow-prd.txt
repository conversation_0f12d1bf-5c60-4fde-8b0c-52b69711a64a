# Workflow Module - Product Requirements Document

## 1. Project Overview

### 1.1 Project Name
RedAI Workflow Module - Visual Workflow Builder

### 1.2 Project Description
Tách EmailAutomationPage thành một module workflow độc lập với visual workflow builder tư<PERSON><PERSON> tự n8n, make.com, và dify. Module này sẽ cho phép người dùng tạo các workflow tự động hóa phức tạp bằng cách kéo thả các node khác nhau.

### 1.3 Project Goals
- Tạo module workflow độc lập từ marketing/email/automation
- Xây dựng visual workflow builder với drag & drop
- Hỗ trợ nhiều loại node kh<PERSON>c nhau (trigger, action, condition, etc.)
- Tích hợp với các service hiện có (email, SMS, database, API)
- Đ<PERSON>m bảo npm run build 100% không lỗi
- UI/UX tương đương n8n và make.com

## 2. Technical Requirements

### 2.1 Architecture
- Module độc lập tại src/modules/workflow
- TypeScript với strict mode
- React 18+ với hooks
- Zustand cho state management
- React Flow cho workflow visualization
- Zod cho validation
- TanStack Query cho API calls

### 2.2 Performance Requirements
- Workflow render < 100ms cho 50 nodes
- Auto-save mỗi 30 giây
- Real-time collaboration support
- Lazy loading cho node types
- Optimized bundle size

### 2.3 Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 3. Functional Requirements

### 3.1 Workflow Builder Core
- Visual drag & drop interface
- Node connection system
- Zoom và pan functionality
- Grid snapping
- Multi-select nodes
- Copy/paste nodes
- Undo/redo operations
- Workflow validation
- Real-time execution preview

### 3.2 Node Types

#### 3.2.1 Trigger Nodes
- Schedule Trigger (cron-based)
- Webhook Trigger
- Email Received Trigger
- Database Change Trigger
- File Upload Trigger
- Form Submission Trigger
- API Call Trigger
- Manual Trigger

#### 3.2.2 Action Nodes
- Send Email
- Send SMS
- Database Operations (CRUD)
- HTTP Request
- File Operations
- Data Transformation
- Notification
- Slack/Discord Message
- Google Sheets Integration
- Export Data

#### 3.2.3 Logic Nodes
- Condition (If/Else)
- Switch (Multiple conditions)
- Loop (For Each)
- Delay/Wait
- Merge Data
- Split Data
- Filter Data
- Sort Data

#### 3.2.4 Integration Nodes
- Google Drive
- Dropbox
- Salesforce
- HubSpot
- Zapier
- Custom API
- Database Connector
- Cloud Storage

### 3.3 Workflow Management
- Create/Edit/Delete workflows
- Workflow templates
- Import/Export workflows
- Workflow versioning
- Workflow sharing
- Workflow analytics
- Execution history
- Error handling và retry logic

### 3.4 Execution Engine
- Sequential execution
- Parallel execution
- Conditional execution
- Error handling
- Retry mechanisms
- Timeout handling
- Resource management
- Execution monitoring

## 4. User Interface Requirements

### 4.1 Layout
- Left sidebar: Node palette
- Center: Workflow canvas
- Right sidebar: Node properties
- Top toolbar: Actions và settings
- Bottom status bar: Execution status

### 4.2 Node Palette
- Categorized node types
- Search functionality
- Drag to canvas
- Node descriptions
- Custom node support

### 4.3 Canvas
- Infinite scrolling
- Zoom controls
- Grid background
- Connection lines
- Node highlighting
- Selection tools

### 4.4 Properties Panel
- Dynamic form based on node type
- Real-time validation
- Help documentation
- Test functionality
- Configuration templates

## 5. Data Models

### 5.1 Workflow
```typescript
interface Workflow {
  id: string;
  name: string;
  description: string;
  nodes: WorkflowNode[];
  connections: Connection[];
  settings: WorkflowSettings;
  status: 'draft' | 'active' | 'paused' | 'error';
  createdAt: Date;
  updatedAt: Date;
  version: number;
}
```

### 5.2 WorkflowNode
```typescript
interface WorkflowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: Record<string, any>;
  inputs: NodeInput[];
  outputs: NodeOutput[];
}
```

### 5.3 Connection
```typescript
interface Connection {
  id: string;
  sourceNodeId: string;
  sourceOutput: string;
  targetNodeId: string;
  targetInput: string;
}
```

## 6. Migration Plan

### 6.1 Phase 1: Module Setup
- Tạo module structure
- Setup TypeScript configs
- Install dependencies
- Create base components

### 6.2 Phase 2: Core Builder
- Implement React Flow integration
- Create node system
- Build drag & drop functionality
- Add connection system

### 6.3 Phase 3: Node Implementation
- Implement trigger nodes
- Implement action nodes
- Implement logic nodes
- Add node validation

### 6.4 Phase 4: Execution Engine
- Build workflow executor
- Add error handling
- Implement retry logic
- Add monitoring

### 6.5 Phase 5: Integration
- Migrate EmailAutomationPage
- Update routing
- Add API endpoints
- Testing và optimization

## 7. Testing Strategy

### 7.1 Unit Tests
- Node functionality
- Workflow validation
- Execution logic
- Data transformations

### 7.2 Integration Tests
- API integrations
- Database operations
- External services
- Error scenarios

### 7.3 E2E Tests
- Workflow creation
- Node connections
- Execution flow
- User interactions

## 8. Success Metrics

### 8.1 Technical Metrics
- Build success rate: 100%
- Test coverage: >90%
- Bundle size: <2MB
- Load time: <3s

### 8.2 User Metrics
- Workflow creation time: <5 minutes
- Node connection success: >95%
- Execution success rate: >98%
- User satisfaction: >4.5/5

## 9. Constraints và Assumptions

### 9.1 Constraints
- Must maintain backward compatibility
- No breaking changes to existing APIs
- Follow RedAI design system
- Support existing authentication

### 9.2 Assumptions
- Users familiar with automation tools
- Stable internet connection
- Modern browser support
- Adequate server resources

## 10. Future Enhancements

### 10.1 Advanced Features
- AI-powered node suggestions
- Workflow templates marketplace
- Real-time collaboration
- Advanced analytics
- Custom node development SDK

### 10.2 Integrations
- More third-party services
- Enterprise connectors
- Custom API builders
- Advanced data transformations
