import { z } from 'zod';

/**
 * Validation schemas cho AI Agents module
 * Tuân thủ quy tắc ProductGuide.md - Section 5: FORM & VALIDATION
 */

// Base schemas
export const agentProfileSchema = z.object({
  name: z.string().min(1, 'Tên agent không được để trống').max(100, 'Tên agent không được quá 100 ký tự'),
  description: z.string().optional(),
  avatar: z.string().url('Avatar phải là URL hợp lệ').optional(),
  birthDate: z.string().optional(),
  gender: z.enum(['male', 'female', 'other']).optional(),
  education: z.string().optional(),
  language: z.string().optional(),
  country: z.string().optional(),
  position: z.string().optional(),
  skills: z.array(z.string()).optional(),
  personality: z.string().optional(),
});

export const modelConfigSchema = z.object({
  temperature: z.number().min(0).max(2).optional(),
  topP: z.number().min(0).max(1).optional(),
  topK: z.number().min(1).optional(),
  maxTokens: z.number().min(1).optional(),
});

export const conversionFieldSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Tên field không được để trống'),
  description: z.string().optional(),
  isEnabled: z.boolean().default(true),
  fieldType: z.enum(['text', 'email', 'phone', 'number', 'date']),
  isRequired: z.boolean().default(false),
});

export const integrationConfigSchema = z.object({
  facebookPages: z.array(z.object({
    id: z.string(),
    name: z.string(),
    accessToken: z.string(),
    isEnabled: z.boolean().default(true),
  })).optional(),
  websites: z.array(z.object({
    id: z.string(),
    url: z.string().url('URL website không hợp lệ'),
    name: z.string(),
    isEnabled: z.boolean().default(true),
  })).optional(),
});

export const strategyConfigSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Tên strategy không được để trống'),
  description: z.string().optional(),
  steps: z.array(z.object({
    id: z.string(),
    name: z.string(),
    description: z.string().optional(),
    order: z.number(),
    configuration: z.record(z.unknown()).optional(),
  })),
  isEnabled: z.boolean().default(true),
});

export const vectorConfigSchema = z.object({
  isEnabled: z.boolean().default(false),
  vectorStoreId: z.string().optional(),
  searchType: z.enum(['similarity', 'mmr', 'similarity_score_threshold']).optional(),
  searchKwargs: z.record(z.unknown()).optional(),
});

export const moduleConfigSchema = z.object({
  id: z.string(),
  name: z.string(),
  isEnabled: z.boolean().default(true),
  configuration: z.record(z.unknown()).optional(),
});

// Main schemas
export const createAgentSchema = z.object({
  profile: agentProfileSchema,
  modelConfig: modelConfigSchema,
  conversionFields: z.array(conversionFieldSchema).optional(),
  integrationConfig: integrationConfigSchema.optional(),
  strategyConfig: strategyConfigSchema.optional(),
  vectorConfig: vectorConfigSchema.optional(),
  modules: z.array(moduleConfigSchema).optional(),
});

// Schema cho CreateAgentModularDto theo hướng dẫn
export const createAgentModularSchema = z.object({
  name: z.string().min(1, 'Tên agent không được để trống').max(255, 'Tên agent tối đa 255 ký tự'),
  typeId: z.number().min(1, 'Type ID không hợp lệ'),
  avatarMimeType: z.string().optional(),
  modelConfig: modelConfigSchema,
  instruction: z.string().optional(),

  // Model Configuration (3 scenarios - chỉ 1 trong 3)
  model_base_id: z.string().optional(),
  model_finetuning_id: z.string().optional(),
  model_id: z.string().optional(),
  provider_id: z.string().optional(),

  // Conditional blocks
  profile: z.object({
    gender: z.string().optional(),
    dateOfBirth: z.number().optional(),
    position: z.string().optional(),
    education: z.string().optional(),
    skills: z.array(z.string()).optional(),
    personality: z.array(z.string()).optional(),
    languages: z.array(z.string()).optional(),
    nations: z.string().optional(),
  }).optional(),

  output: z.object({
    facebookPageIds: z.array(z.string()).optional(),
    userWebsiteIds: z.array(z.string()).optional(),
  }).optional(),

  resources: z.object({
    urlIds: z.array(z.string()).optional(),
    mediaIds: z.array(z.string()).optional(),
    productIds: z.array(z.string()).optional(),
  }).optional(),

  strategy: z.object({
    strategyId: z.string().optional(),
  }).optional(),

  multiAgent: z.object({
    isEnabled: z.boolean().optional(),
    coordinatorId: z.string().optional(),
    subAgentIds: z.array(z.string()).optional(),
  }).optional(),
}).refine((data) => {
  // Validation: Ít nhất 1 trong 3 model scenarios phải được cung cấp
  const hasBaseModel = !!data.model_base_id;
  const hasFinetuningModel = !!data.model_finetuning_id;
  const hasPersonalModel = !!(data.model_id && data.provider_id);

  return hasBaseModel || hasFinetuningModel || hasPersonalModel;
}, {
  message: 'Phải chọn ít nhất một loại model (base, finetuning, hoặc personal)',
}).refine((data) => {
  // Validation: model_id và provider_id phải đi cùng nhau
  if (data.model_id && !data.provider_id) {
    return false;
  }
  if (!data.model_id && data.provider_id) {
    return false;
  }
  return true;
}, {
  message: 'model_id và provider_id phải được cung cấp cùng nhau',
});

export const updateAgentSchema = createAgentSchema.partial();

export const agentQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  sortBy: z.enum(['name', 'createdAt', 'updatedAt']).optional(),
  sortDirection: z.enum(['asc', 'desc']).default('desc'),
  status: z.enum(['active', 'inactive', 'all']).optional(),
  category: z.string().optional(),
});

// Type Agent schemas
export const typeAgentSchema = z.object({
  name: z.string().min(1, 'Tên type agent không được để trống'),
  description: z.string().optional(),
  avatar: z.string().url('Avatar phải là URL hợp lệ').optional(),
  category: z.string().optional(),
  isSystem: z.boolean().default(false),
  defaultConfig: z.object({
    hasProfile: z.boolean().default(true),
    hasModel: z.boolean().default(true),
    hasConversion: z.boolean().default(false),
    hasIntegration: z.boolean().default(false),
    hasStrategy: z.boolean().default(false),
    hasVector: z.boolean().default(false),
    hasModules: z.boolean().default(false),
  }),
});

export const createTypeAgentSchema = typeAgentSchema;
export const updateTypeAgentSchema = typeAgentSchema.partial();

export const typeAgentQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  sortBy: z.enum(['name', 'createdAt']).optional(),
  sortDirection: z.enum(['asc', 'desc']).default('desc'),
  isSystem: z.boolean().optional(),
  category: z.string().optional(),
});

// Base Model schemas
export const baseModelQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  provider: z.enum(['openai', 'anthropic', 'google', 'meta', 'xai']).optional(),
  sortBy: z.enum(['name', 'provider', 'createdAt']).optional(),
  sortDirection: z.enum(['asc', 'desc']).default('desc'),
});

// Form data types (inferred from schemas)
export type AgentProfileFormData = z.infer<typeof agentProfileSchema>;
export type ModelConfigFormData = z.infer<typeof modelConfigSchema>;
export type ConversionFieldFormData = z.infer<typeof conversionFieldSchema>;
export type IntegrationConfigFormData = z.infer<typeof integrationConfigSchema>;
export type StrategyConfigFormData = z.infer<typeof strategyConfigSchema>;
export type VectorConfigFormData = z.infer<typeof vectorConfigSchema>;
export type ModuleConfigFormData = z.infer<typeof moduleConfigSchema>;

export type CreateAgentFormData = z.infer<typeof createAgentSchema>;
export type CreateAgentModularFormData = z.infer<typeof createAgentModularSchema>;
export type UpdateAgentFormData = z.infer<typeof updateAgentSchema>;
export type AgentQueryFormData = z.infer<typeof agentQuerySchema>;

export type TypeAgentFormData = z.infer<typeof typeAgentSchema>;
export type CreateTypeAgentFormData = z.infer<typeof createTypeAgentSchema>;
export type UpdateTypeAgentFormData = z.infer<typeof updateTypeAgentSchema>;
export type TypeAgentQueryFormData = z.infer<typeof typeAgentQuerySchema>;

export type BaseModelQueryFormData = z.infer<typeof baseModelQuerySchema>;

// Validation helper functions
export const validateAgentProfile = (data: unknown) => {
  return agentProfileSchema.safeParse(data);
};

export const validateModelConfig = (data: unknown) => {
  return modelConfigSchema.safeParse(data);
};

export const validateCreateAgent = (data: unknown) => {
  return createAgentSchema.safeParse(data);
};

export const validateUpdateAgent = (data: unknown) => {
  return updateAgentSchema.safeParse(data);
};

export const validateAgentQuery = (data: unknown) => {
  return agentQuerySchema.safeParse(data);
};

export const validateTypeAgent = (data: unknown) => {
  return typeAgentSchema.safeParse(data);
};

export const validateTypeAgentQuery = (data: unknown) => {
  return typeAgentQuerySchema.safeParse(data);
};

export const validateBaseModelQuery = (data: unknown) => {
  return baseModelQuerySchema.safeParse(data);
};
