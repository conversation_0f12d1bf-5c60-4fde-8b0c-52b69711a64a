import { Card, IconCard } from '@/shared/components/common';
import React, { useMemo } from 'react';

export interface TypeAgent {
  id: number;
  name: string;
  description: string;
  countTool: number;
  // Cấu hình từ API
  config: {
    hasProfile: boolean;
    hasOutput: boolean;
    hasConversion: boolean;
    hasResources: boolean;
    hasStrategy: boolean;
    hasMultiAgent: boolean;
  };
}

export interface TypeAgentProps {
  agent: TypeAgent;
  isSelected?: boolean;
  onClick?: (id: number) => void;
  onView?: (id: number) => void;
  showActions?: boolean; // C<PERSON> hiển thị các action buttons không
}

// Danh sách các icon có thể sử dụng
// const AVAILABLE_ICONS: IconName[] = [
//   'chat', 'user', 'star', 'settings', 'code',
//   'document', 'globe', 'chart', 'award', 'integration'
// ];

// Danh sách các màu có thể sử dụng
const AVAILABLE_COLORS = [
  'text-red-500', 'text-blue-500', 'text-green-500',
  'text-purple-500', 'text-yellow-500', 'text-indigo-500'
];

// Danh sách các màu nền có thể sử dụng
const AVAILABLE_BG_COLORS = [
  'bg-red-100 dark:bg-red-900',
  'bg-blue-100 dark:bg-blue-900',
  'bg-green-100 dark:bg-green-900',
  'bg-purple-100 dark:bg-purple-900',
  'bg-yellow-100 dark:bg-yellow-900',
  'bg-indigo-100 dark:bg-indigo-900'
];

/**
 * Component hiển thị thông tin của Type Agent
 */
const TypeAgentCard: React.FC<TypeAgentProps> = ({
  agent,
  isSelected = false,
  onClick,
  onView,
  showActions = false
}) => {
  // Tạo icon và màu ngẫu nhiên dựa trên ID của agent
  const { icon, iconColor, bgColor } = useMemo(() => {
    // Sử dụng ID để tạo số ngẫu nhiên nhưng ổn định
    const colorIndex = (agent.id * 3) % AVAILABLE_COLORS.length;
    const bgColorIndex = (agent.id * 7) % AVAILABLE_BG_COLORS.length;

    return {
      icon: 'user',
      iconColor: AVAILABLE_COLORS[colorIndex],
      bgColor: AVAILABLE_BG_COLORS[bgColorIndex]
    };
  }, [agent.id]);

  // Xử lý khi click vào card
  const handleClick = () => {
    if (onClick) {
      onClick(agent.id);
    }
  };

  // Xử lý khi click vào icon xem
  const handleView = () => {
    if (onView) {
      onView(agent.id);
    }
  };

  return (
    <Card
      className={`h-full overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer ${isSelected
        ? 'ring-2 ring-primary ring-offset-2 dark:ring-offset-gray-800 bg-primary-50 dark:bg-primary-900/20'
        : 'hover:border-primary-300 dark:hover:border-primary-700'
        }`}
      variant="elevated"
      onClick={handleClick}
    >
      <div className="p-4 relative">
        {/* Action buttons - hiển thị ở góc phải trên */}
        {showActions && onView && (
          <div className="absolute top-2 right-2 flex gap-1 z-10">
            <div onClick={(e) => { e.stopPropagation(); handleView(); }}>
              <IconCard
                icon="eye"
                variant="secondary"
                size="sm"
                onClick={handleView}
                className="hover:bg-blue-100 dark:hover:bg-blue-900 text-blue-600 dark:text-blue-400"
                title="Xem chi tiết"
              />
            </div>
          </div>
        )}

        <div className="flex flex-col space-y-4">
          {/* Hàng 1: Avatar và thông tin */}
          <div className="flex items-center gap-3 overflow-hidden">
            {/* Avatar */}
            <div className="relative w-12 h-12 flex-shrink-0">
              <div className="w-full h-full relative">
                <div className={`absolute inset-0 flex items-center justify-center z-0 ${bgColor} rounded-full`}>
                  <IconCard
                    icon={icon}
                    variant="primary"
                    size="md"
                    className={iconColor}
                  />
                </div>
              </div>
            </div>

            {/* Thông tin agent: tên */}
            <div className="flex flex-col min-w-0 flex-grow">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
                <div className="min-w-0">
                  <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                    {agent.name}
                  </h3>
                </div>
              </div>
            </div>
          </div>

          {/* Hàng 2: Mô tả */}
          <div className="text-sm text-gray-600 dark:text-gray-300">
            {agent.description}
          </div>

          {/* Hàng 3: Ngày tạo */}
          <div className="flex justify-between items-center pt-2">
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Ngày tạo: {agent.countTool}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default TypeAgentCard;
