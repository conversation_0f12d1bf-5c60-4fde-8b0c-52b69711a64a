/**
 * Provider Model Validation Schemas
 */

import { z } from 'zod';
import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';

/**
 * Schema cho thông tin provider model
 */
export const providerModelSchema = z.object({
  id: z.string().optional(),
  name: z
    .string()
    .min(1, 'Tên không được để trống')
    .max(255, 'Tên không được vượt quá 255 ký tự')
    .trim(),
  type: z.nativeEnum(TypeProviderEnum, {
    errorMap: () => ({ message: 'Loại nhà cung cấp không hợp lệ' })
  }),
  apiKey: z
    .string()
    .min(1, 'API key không được để trống')
    .min(10, 'API key phải có ít nhất 10 ký tự')
    .optional(),
  createdAt: z.number().optional(),
  updatedAt: z.number().optional(),
});

/**
 * Schema cho tạo provider model mới
 */
export const createProviderModelSchema = z.object({
  name: z
    .string()
    .min(1, 'Tên không được để trống')
    .max(255, 'Tên không được vượt quá 255 ký tự')
    .trim(),
  type: z.nativeEnum(TypeProviderEnum, {
    errorMap: () => ({ message: 'Loại nhà cung cấp không hợp lệ' })
  }),
  apiKey: z
    .string()
    .min(1, 'API key không được để trống')
    .min(10, 'API key phải có ít nhất 10 ký tự')
    .regex(/^[a-zA-Z0-9\-_]+$/, 'API key chỉ được chứa chữ cái, số, dấu gạch ngang và gạch dưới'),
});

/**
 * Schema cho cập nhật provider model
 */
export const updateProviderModelSchema = z.object({
  name: z
    .string()
    .min(1, 'Tên không được để trống')
    .max(255, 'Tên không được vượt quá 255 ký tự')
    .trim()
    .optional(),
  apiKey: z
    .string()
    .min(1, 'API key không được để trống')
    .min(10, 'API key phải có ít nhất 10 ký tự')
    .regex(/^[a-zA-Z0-9\-_]+$/, 'API key chỉ được chứa chữ cái, số, dấu gạch ngang và gạch dưới')
    .optional(),
});

/**
 * Schema cho query parameters
 */
export const providerModelQuerySchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['ASC', 'DESC']).optional(),
  type: z.nativeEnum(TypeProviderEnum).optional(),
});

/**
 * Schema cho danh sách provider model
 */
export const providerModelListSchema = z.array(
  z.object({
    id: z.string(),
    name: z.string(),
    type: z.nativeEnum(TypeProviderEnum),
    createdAt: z.number(),
  })
);

/**
 * Schema cho kết quả phân trang
 */
export const paginatedProviderModelSchema = z.object({
  items: providerModelListSchema,
  meta: z.object({
    totalItems: z.number(),
    itemCount: z.number(),
    itemsPerPage: z.number(),
    totalPages: z.number(),
    currentPage: z.number(),
  }),
});

/**
 * Schema cho API response
 */
export const providerModelApiResponseSchema = <T extends z.ZodTypeAny>(resultSchema: T) =>
  z.object({
    code: z.number(),
    message: z.string(),
    result: resultSchema,
  });

// Export type definitions
export type ProviderModelFormData = z.infer<typeof createProviderModelSchema>;
export type UpdateProviderModelFormData = z.infer<typeof updateProviderModelSchema>;
export type ProviderModelQueryFormData = z.infer<typeof providerModelQuerySchema>;
export type ProviderModelListData = z.infer<typeof providerModelListSchema>;
export type PaginatedProviderModelData = z.infer<typeof paginatedProviderModelSchema>;
