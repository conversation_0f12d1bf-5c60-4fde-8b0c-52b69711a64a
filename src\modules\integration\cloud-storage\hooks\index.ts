import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type {
  CloudStorageProviderQueryParams,
  CreateCloudStorageProviderDto,
  UpdateCloudStorageProviderDto,
  TestCloudStorageProviderDto,
  TestCloudStorageProviderWithConfigDto,
  FileUploadRequest,
  FileDownloadRequest,
  FolderCreateRequest,
  FileSearchRequest,
  BatchOperationRequest,
  ShareLinkRequest,
} from '../types';
import * as cloudStorageServices from '../services';

/**
 * Query Keys
 */
export const CLOUD_STORAGE_QUERY_KEYS = {
  ALL: ['cloudStorage'] as const,
  PROVIDERS: () => [...CLOUD_STORAGE_QUERY_KEYS.ALL, 'providers'] as const,
  PROVIDERS_LIST: (params: CloudStorageProviderQueryParams) => [...CLOUD_STORAGE_QUERY_KEYS.PROVIDERS(), 'list', params] as const,
  PROVIDER: (id: number) => [...CLOUD_STORAGE_QUERY_KEYS.PROVIDERS(), 'detail', id] as const,
  FILES: (providerId: number, folderId?: string) => [...CLOUD_STORAGE_QUERY_KEYS.ALL, 'files', providerId, folderId] as const,
  FOLDERS: (providerId: number, parentId?: string) => [...CLOUD_STORAGE_QUERY_KEYS.ALL, 'folders', providerId, parentId] as const,
  SYNC_STATUS: (providerId: number) => [...CLOUD_STORAGE_QUERY_KEYS.ALL, 'syncStatus', providerId] as const,
  STORAGE_QUOTA: (providerId: number) => [...CLOUD_STORAGE_QUERY_KEYS.ALL, 'storageQuota', providerId] as const,
} as const;

/**
 * Provider Configuration Hooks
 */
export const useCloudStorageProviders = (params?: CloudStorageProviderQueryParams) => {
  return useQuery({
    queryKey: CLOUD_STORAGE_QUERY_KEYS.PROVIDERS_LIST(params || {}),
    queryFn: () => cloudStorageServices.getCloudStorageProvidersWithBusinessLogic(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCloudStorageProvider = (id: number) => {
  return useQuery({
    queryKey: CLOUD_STORAGE_QUERY_KEYS.PROVIDER(id),
    queryFn: () => cloudStorageServices.getCloudStorageProviderWithBusinessLogic(id),
    enabled: !!id && id > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateCloudStorageProvider = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCloudStorageProviderDto) => 
      cloudStorageServices.createCloudStorageProviderWithBusinessLogic(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CLOUD_STORAGE_QUERY_KEYS.PROVIDERS() });
    },
  });
};

export const useUpdateCloudStorageProvider = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateCloudStorageProviderDto }) =>
      cloudStorageServices.updateCloudStorageProviderWithBusinessLogic(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: CLOUD_STORAGE_QUERY_KEYS.PROVIDERS() });
      queryClient.invalidateQueries({ queryKey: CLOUD_STORAGE_QUERY_KEYS.PROVIDER(id) });
    },
  });
};

export const useDeleteCloudStorageProvider = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => cloudStorageServices.deleteCloudStorageProviderWithBusinessLogic(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CLOUD_STORAGE_QUERY_KEYS.PROVIDERS() });
    },
  });
};

export const useTestCloudStorageProvider = () => {
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data?: TestCloudStorageProviderDto }) =>
      cloudStorageServices.testCloudStorageProviderWithBusinessLogic(id, data),
  });
};

export const useTestCloudStorageProviderWithConfig = () => {
  return useMutation({
    mutationFn: (data: TestCloudStorageProviderWithConfigDto) =>
      cloudStorageServices.testCloudStorageProviderWithConfigBusinessLogic(data),
  });
};

/**
 * OAuth Hooks
 */
export const useGetOAuthUrl = () => {
  return useMutation({
    mutationFn: (providerId: number) => cloudStorageServices.getOAuthUrlWithBusinessLogic(providerId),
  });
};

export const useHandleOAuthCallback = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ providerId, code, state }: { providerId: number; code: string; state?: string }) =>
      cloudStorageServices.handleOAuthCallbackWithBusinessLogic(providerId, code, state),
    onSuccess: (_, { providerId }) => {
      queryClient.invalidateQueries({ queryKey: CLOUD_STORAGE_QUERY_KEYS.PROVIDER(providerId) });
    },
  });
};

/**
 * File Management Hooks
 */
export const useFiles = (providerId: number, folderId?: string) => {
  return useQuery({
    queryKey: CLOUD_STORAGE_QUERY_KEYS.FILES(providerId, folderId),
    queryFn: () => cloudStorageServices.getFilesWithBusinessLogic(providerId, folderId),
    enabled: !!providerId && providerId > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useFolders = (providerId: number, parentId?: string) => {
  return useQuery({
    queryKey: CLOUD_STORAGE_QUERY_KEYS.FOLDERS(providerId, parentId),
    queryFn: () => cloudStorageServices.getFoldersWithBusinessLogic(providerId, parentId),
    enabled: !!providerId && providerId > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useUploadFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ providerId, data }: { providerId: number; data: FileUploadRequest }) =>
      cloudStorageServices.uploadFileWithBusinessLogic(providerId, data),
    onSuccess: (_, { providerId, data }) => {
      queryClient.invalidateQueries({ queryKey: CLOUD_STORAGE_QUERY_KEYS.FILES(providerId, data.parentFolderId) });
      queryClient.invalidateQueries({ queryKey: CLOUD_STORAGE_QUERY_KEYS.STORAGE_QUOTA(providerId) });
    },
  });
};

export const useDownloadFile = () => {
  return useMutation({
    mutationFn: ({ providerId, data }: { providerId: number; data: FileDownloadRequest }) =>
      cloudStorageServices.downloadFileWithBusinessLogic(providerId, data),
  });
};

export const useDeleteFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ providerId, fileId }: { providerId: number; fileId: string }) =>
      cloudStorageServices.deleteFileWithBusinessLogic(providerId, fileId),
    onSuccess: (_, { providerId }) => {
      queryClient.invalidateQueries({ queryKey: CLOUD_STORAGE_QUERY_KEYS.FILES(providerId) });
      queryClient.invalidateQueries({ queryKey: CLOUD_STORAGE_QUERY_KEYS.STORAGE_QUOTA(providerId) });
    },
  });
};

export const useCreateFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ providerId, data }: { providerId: number; data: FolderCreateRequest }) =>
      cloudStorageServices.createFolderWithBusinessLogic(providerId, data),
    onSuccess: (_, { providerId, data }) => {
      queryClient.invalidateQueries({ queryKey: CLOUD_STORAGE_QUERY_KEYS.FOLDERS(providerId, data.parentFolderId) });
    },
  });
};

export const useDeleteFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ providerId, folderId }: { providerId: number; folderId: string }) =>
      cloudStorageServices.deleteFolderWithBusinessLogic(providerId, folderId),
    onSuccess: (_, { providerId }) => {
      queryClient.invalidateQueries({ queryKey: CLOUD_STORAGE_QUERY_KEYS.FOLDERS(providerId) });
    },
  });
};

/**
 * Search Hooks
 */
export const useSearchFiles = () => {
  return useMutation({
    mutationFn: ({ providerId, data }: { providerId: number; data: FileSearchRequest }) =>
      cloudStorageServices.searchFilesWithBusinessLogic(providerId, data),
  });
};

/**
 * Batch Operations Hooks
 */
export const useBatchOperation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ providerId, data }: { providerId: number; data: BatchOperationRequest }) =>
      cloudStorageServices.batchOperationWithBusinessLogic(providerId, data),
    onSuccess: (_, { providerId }) => {
      queryClient.invalidateQueries({ queryKey: CLOUD_STORAGE_QUERY_KEYS.FILES(providerId) });
      queryClient.invalidateQueries({ queryKey: CLOUD_STORAGE_QUERY_KEYS.FOLDERS(providerId) });
    },
  });
};

/**
 * Share Hooks
 */
export const useCreateShareLink = () => {
  return useMutation({
    mutationFn: ({ providerId, data }: { providerId: number; data: ShareLinkRequest }) =>
      cloudStorageServices.createShareLinkWithBusinessLogic(providerId, data),
  });
};

export const useRevokeShareLink = () => {
  return useMutation({
    mutationFn: ({ providerId, fileId }: { providerId: number; fileId: string }) =>
      cloudStorageServices.revokeShareLinkWithBusinessLogic(providerId, fileId),
  });
};

/**
 * Sync Hooks
 */
export const useSyncProvider = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (providerId: number) => cloudStorageServices.syncProviderWithBusinessLogic(providerId),
    onSuccess: (_, providerId) => {
      queryClient.invalidateQueries({ queryKey: CLOUD_STORAGE_QUERY_KEYS.SYNC_STATUS(providerId) });
      queryClient.invalidateQueries({ queryKey: CLOUD_STORAGE_QUERY_KEYS.FILES(providerId) });
      queryClient.invalidateQueries({ queryKey: CLOUD_STORAGE_QUERY_KEYS.FOLDERS(providerId) });
    },
  });
};

export const useSyncStatus = (providerId: number) => {
  return useQuery({
    queryKey: CLOUD_STORAGE_QUERY_KEYS.SYNC_STATUS(providerId),
    queryFn: () => cloudStorageServices.getSyncStatusWithBusinessLogic(providerId),
    enabled: !!providerId && providerId > 0,
    refetchInterval: 5000, // Refetch every 5 seconds when syncing
    staleTime: 1000, // 1 second
  });
};

/**
 * Storage Quota Hooks
 */
export const useStorageQuota = (providerId: number) => {
  return useQuery({
    queryKey: CLOUD_STORAGE_QUERY_KEYS.STORAGE_QUOTA(providerId),
    queryFn: () => cloudStorageServices.getStorageQuotaWithBusinessLogic(providerId),
    enabled: !!providerId && providerId > 0,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};
