import type {
  CreateCloudStorageProviderDto,
  UpdateCloudStorageProviderDto,
  TestCloudStorageProviderDto,
  TestCloudStorageProviderWithConfigDto,
  CloudStorageProviderQueryParams,
  FileUploadRequest,
  FileDownloadRequest,
  FolderCreateRequest,
  FileSearchRequest,
  BatchOperationRequest,
  ShareLinkRequest,
} from '../types';
import * as cloudStorageApi from '../api';

/**
 * Cloud Storage Services - Business Logic Layer
 */

// Provider Configuration Services
export const getCloudStorageProvidersWithBusinessLogic = async (params?: CloudStorageProviderQueryParams) => {
  const defaultParams = {
    page: 1,
    limit: 10,
    ...params,
  };

  // Validate limit
  if (defaultParams.limit > 100) {
    throw new Error('Limit cannot exceed 100');
  }

  return cloudStorageApi.getCloudStorageProviders(defaultParams);
};

export const getCloudStorageProviderWithBusinessLogic = async (id: number) => {
  if (!id || id <= 0) {
    throw new Error('Invalid provider ID');
  }

  return cloudStorageApi.getCloudStorageProvider(id);
};

export const createCloudStorageProviderWithBusinessLogic = async (data: CreateCloudStorageProviderDto) => {
  // Validate required fields
  if (!data.providerType) {
    throw new Error('Provider type is required');
  }

  if (!data.providerName?.trim()) {
    throw new Error('Provider name is required');
  }

  if (!data.clientId?.trim()) {
    throw new Error('Client ID is required');
  }

  if (!data.clientSecret?.trim()) {
    throw new Error('Client Secret is required');
  }

  if (!data.refreshToken?.trim()) {
    throw new Error('Refresh Token is required');
  }

  // Validate sync folders if provided
  if (data.syncFolders && data.syncFolders.length > 0) {
    const invalidFolders = data.syncFolders.filter(folder => !folder.trim());
    if (invalidFolders.length > 0) {
      throw new Error('All sync folders must have valid names');
    }
  }

  return cloudStorageApi.createCloudStorageProvider(data);
};

export const updateCloudStorageProviderWithBusinessLogic = async (id: number, data: UpdateCloudStorageProviderDto) => {
  if (!id || id <= 0) {
    throw new Error('Invalid provider ID');
  }

  // Validate fields if provided
  if (data.providerName !== undefined && !data.providerName?.trim()) {
    throw new Error('Provider name cannot be empty');
  }

  if (data.clientId !== undefined && !data.clientId?.trim()) {
    throw new Error('Client ID cannot be empty');
  }

  if (data.clientSecret !== undefined && !data.clientSecret?.trim()) {
    throw new Error('Client Secret cannot be empty');
  }

  if (data.refreshToken !== undefined && !data.refreshToken?.trim()) {
    throw new Error('Refresh Token cannot be empty');
  }

  return cloudStorageApi.updateCloudStorageProvider(id, data);
};

export const deleteCloudStorageProviderWithBusinessLogic = async (id: number) => {
  if (!id || id <= 0) {
    throw new Error('Invalid provider ID');
  }

  return cloudStorageApi.deleteCloudStorageProvider(id);
};

export const testCloudStorageProviderWithBusinessLogic = async (id: number, data?: TestCloudStorageProviderDto) => {
  if (!id || id <= 0) {
    throw new Error('Invalid provider ID');
  }

  return cloudStorageApi.testCloudStorageProvider(id, data);
};

export const testCloudStorageProviderWithConfigBusinessLogic = async (data: TestCloudStorageProviderWithConfigDto) => {
  // Validate config
  if (!data.storageConfig.providerType) {
    throw new Error('Provider type is required');
  }

  if (!data.storageConfig.clientId?.trim()) {
    throw new Error('Client ID is required');
  }

  if (!data.storageConfig.clientSecret?.trim()) {
    throw new Error('Client Secret is required');
  }

  return cloudStorageApi.testCloudStorageProviderWithConfig(data);
};

// OAuth Services
export const getOAuthUrlWithBusinessLogic = async (providerId: number) => {
  if (!providerId || providerId <= 0) {
    throw new Error('Invalid provider ID');
  }

  return cloudStorageApi.getOAuthUrl(providerId);
};

export const handleOAuthCallbackWithBusinessLogic = async (providerId: number, code: string, state?: string) => {
  if (!providerId || providerId <= 0) {
    throw new Error('Invalid provider ID');
  }

  if (!code?.trim()) {
    throw new Error('Authorization code is required');
  }

  return cloudStorageApi.handleOAuthCallback(providerId, code, state);
};

// File Management Services
export const getFilesWithBusinessLogic = async (providerId: number, folderId?: string) => {
  if (!providerId || providerId <= 0) {
    throw new Error('Invalid provider ID');
  }

  return cloudStorageApi.getFiles(providerId, folderId);
};

export const getFoldersWithBusinessLogic = async (providerId: number, parentId?: string) => {
  if (!providerId || providerId <= 0) {
    throw new Error('Invalid provider ID');
  }

  return cloudStorageApi.getFolders(providerId, parentId);
};

export const uploadFileWithBusinessLogic = async (providerId: number, data: FileUploadRequest) => {
  if (!providerId || providerId <= 0) {
    throw new Error('Invalid provider ID');
  }

  if (!data.fileName?.trim()) {
    throw new Error('File name is required');
  }

  if (!data.fileContent) {
    throw new Error('File content is required');
  }

  // Validate file size (example: 100MB limit)
  const maxSize = 100 * 1024 * 1024; // 100MB
  if (data.fileContent.size > maxSize) {
    throw new Error('File size cannot exceed 100MB');
  }

  return cloudStorageApi.uploadFile(providerId, data);
};

export const downloadFileWithBusinessLogic = async (providerId: number, data: FileDownloadRequest) => {
  if (!providerId || providerId <= 0) {
    throw new Error('Invalid provider ID');
  }

  if (!data.fileId?.trim()) {
    throw new Error('File ID is required');
  }

  return cloudStorageApi.downloadFile(providerId, data);
};

export const deleteFileWithBusinessLogic = async (providerId: number, fileId: string) => {
  if (!providerId || providerId <= 0) {
    throw new Error('Invalid provider ID');
  }

  if (!fileId?.trim()) {
    throw new Error('File ID is required');
  }

  return cloudStorageApi.deleteFile(providerId, fileId);
};

export const createFolderWithBusinessLogic = async (providerId: number, data: FolderCreateRequest) => {
  if (!providerId || providerId <= 0) {
    throw new Error('Invalid provider ID');
  }

  if (!data.folderName?.trim()) {
    throw new Error('Folder name is required');
  }

  // Validate folder name (no special characters)
  const invalidChars = /[<>:"/\\|?*]/;
  if (invalidChars.test(data.folderName)) {
    throw new Error('Folder name contains invalid characters');
  }

  return cloudStorageApi.createFolder(providerId, data);
};

export const deleteFolderWithBusinessLogic = async (providerId: number, folderId: string) => {
  if (!providerId || providerId <= 0) {
    throw new Error('Invalid provider ID');
  }

  if (!folderId?.trim()) {
    throw new Error('Folder ID is required');
  }

  return cloudStorageApi.deleteFolder(providerId, folderId);
};

// Search Services
export const searchFilesWithBusinessLogic = async (providerId: number, data: FileSearchRequest) => {
  if (!providerId || providerId <= 0) {
    throw new Error('Invalid provider ID');
  }

  if (!data.query?.trim()) {
    throw new Error('Search query is required');
  }

  // Limit max results
  const maxResults = data.maxResults || 50;
  if (maxResults > 100) {
    throw new Error('Max results cannot exceed 100');
  }

  return cloudStorageApi.searchFiles(providerId, { ...data, maxResults });
};

// Batch Operations Services
export const batchOperationWithBusinessLogic = async (providerId: number, data: BatchOperationRequest) => {
  if (!providerId || providerId <= 0) {
    throw new Error('Invalid provider ID');
  }

  if (!data.fileIds || data.fileIds.length === 0) {
    throw new Error('File IDs are required');
  }

  // Limit batch size
  if (data.fileIds.length > 50) {
    throw new Error('Cannot process more than 50 files at once');
  }

  if ((data.operation === 'copy' || data.operation === 'move') && !data.targetFolderId) {
    throw new Error('Target folder ID is required for copy/move operations');
  }

  return cloudStorageApi.batchOperation(providerId, data);
};

// Share Services
export const createShareLinkWithBusinessLogic = async (providerId: number, data: ShareLinkRequest) => {
  if (!providerId || providerId <= 0) {
    throw new Error('Invalid provider ID');
  }

  if (!data.fileId?.trim()) {
    throw new Error('File ID is required');
  }

  // Validate expiration time if provided
  if (data.expirationTime) {
    const expirationDate = new Date(data.expirationTime);
    const now = new Date();
    if (expirationDate <= now) {
      throw new Error('Expiration time must be in the future');
    }
  }

  return cloudStorageApi.createShareLink(providerId, data);
};

export const revokeShareLinkWithBusinessLogic = async (providerId: number, fileId: string) => {
  if (!providerId || providerId <= 0) {
    throw new Error('Invalid provider ID');
  }

  if (!fileId?.trim()) {
    throw new Error('File ID is required');
  }

  return cloudStorageApi.revokeShareLink(providerId, fileId);
};

// Sync Services
export const syncProviderWithBusinessLogic = async (providerId: number) => {
  if (!providerId || providerId <= 0) {
    throw new Error('Invalid provider ID');
  }

  return cloudStorageApi.syncProvider(providerId);
};

export const getSyncStatusWithBusinessLogic = async (providerId: number) => {
  if (!providerId || providerId <= 0) {
    throw new Error('Invalid provider ID');
  }

  return cloudStorageApi.getSyncStatus(providerId);
};

// Storage Quota Services
export const getStorageQuotaWithBusinessLogic = async (providerId: number) => {
  if (!providerId || providerId <= 0) {
    throw new Error('Invalid provider ID');
  }

  return cloudStorageApi.getStorageQuota(providerId);
};
