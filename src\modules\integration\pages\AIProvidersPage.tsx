import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typo<PERSON>,
  Card,
  Button,
  SearchBar,
  Modal,
  Form,
  FormItem,
  FormGrid,
  Input,
  Toggle,
  IconCard,
  ModernMenu,
  Tooltip,
  Container,
  Badge,
  Icon,
} from '@/shared/components/common';
import Table from '@/shared/components/common/Table';
import { TableColumn } from '@/shared/components/common/Table/types';

// Định nghĩa kiểu dữ liệu cho AIProvider
interface AIProvider {
  id: string;
  name: string;
  description: string;
  baseUrl: string;
  apiVersion: string;
  isActive: boolean;
  createdAt: string;
  models: string[];
  maxTokens: number | null;
  providerType?: string; // Thêm field để map với icon
}

/**
 * Function để lấy icon dựa trên tên provider
 */
const getProviderIcon = (providerName: string): string => {
  const providerIconMap: Record<string, string> = {
    'OpenAI': 'openai',
    'Anthropic': 'anthropic',
    'Google AI': 'google',
    'Meta': 'meta',
    'DeepSeek': 'deepseek',
    'Grok': 'grok',
    'XAI': 'grok',
    'Cohere': 'robot',
    'Mistral AI': 'robot',
    'Stability AI': 'robot',
    'Replicate': 'robot',
    'Hugging Face': 'robot',
  };

  return providerIconMap[providerName] || 'robot';
};

/**
 * Dữ liệu mẫu cho nhà cung cấp AI
 */
const mockAIProviders = [
  {
    id: '1',
    name: 'OpenAI',
    description: 'Nhà cung cấp các mô hình AI tiên tiến như GPT-4, GPT-3.5',
    baseUrl: 'https://api.openai.com',
    apiVersion: 'v1',
    isActive: true,
    createdAt: '2023-01-15T08:30:00Z',
    models: ['GPT-4', 'GPT-3.5 Turbo', 'DALL-E 3', 'Whisper'],
    maxTokens: 128000,
  },
  {
    id: '2',
    name: 'Anthropic',
    description: 'Nhà cung cấp mô hình Claude và các công nghệ AI an toàn',
    baseUrl: 'https://api.anthropic.com',
    apiVersion: 'v1',
    isActive: true,
    createdAt: '2023-02-20T10:15:00Z',
    models: ['Claude 3 Opus', 'Claude 3 Sonnet', 'Claude 3 Haiku'],
    maxTokens: 200000,
  },
  {
    id: '3',
    name: 'Google AI',
    description: 'Cung cấp các mô hình Gemini và các dịch vụ AI của Google',
    baseUrl: 'https://generativelanguage.googleapis.com',
    apiVersion: 'v1',
    isActive: false,
    createdAt: '2023-03-10T14:45:00Z',
    models: ['Gemini Pro', 'Gemini Ultra', 'PaLM 2'],
    maxTokens: 32000,
  },
  {
    id: '4',
    name: 'Cohere',
    description: 'Chuyên về các mô hình ngôn ngữ và embedding',
    baseUrl: 'https://api.cohere.ai',
    apiVersion: 'v1',
    isActive: true,
    createdAt: '2023-04-05T09:20:00Z',
    models: ['Command R', 'Command R+', 'Embed English'],
    maxTokens: 128000,
  },
  {
    id: '5',
    name: 'Mistral AI',
    description: 'Cung cấp các mô hình ngôn ngữ hiệu quả và mạnh mẽ',
    baseUrl: 'https://api.mistral.ai',
    apiVersion: 'v1',
    isActive: true,
    createdAt: '2023-05-12T11:30:00Z',
    models: ['Mistral Large', 'Mistral Medium', 'Mistral Small'],
    maxTokens: 32000,
  },
  {
    id: '6',
    name: 'Stability AI',
    description: 'Chuyên về các mô hình tạo hình ảnh AI',
    baseUrl: 'https://api.stability.ai',
    apiVersion: 'v1',
    isActive: true,
    createdAt: '2023-06-18T13:45:00Z',
    models: ['Stable Diffusion XL', 'Stable Diffusion 3', 'Stable Video'],
    maxTokens: null,
  },
  {
    id: '7',
    name: 'Replicate',
    description: 'Nền tảng chạy các mô hình AI mã nguồn mở',
    baseUrl: 'https://api.replicate.com',
    apiVersion: 'v1',
    isActive: true,
    createdAt: '2023-07-22T15:20:00Z',
    models: ['Llama 3', 'SDXL Lightning', 'MusicGen'],
    maxTokens: null,
  },
  {
    id: '8',
    name: 'Hugging Face',
    description: 'Nền tảng cung cấp hàng ngàn mô hình AI mã nguồn mở',
    baseUrl: 'https://api-inference.huggingface.co',
    apiVersion: 'v1',
    isActive: false,
    createdAt: '2023-08-30T09:10:00Z',
    models: ['BERT', 'T5', 'Falcon'],
    maxTokens: null,
  },
  {
    id: '9',
    name: 'DeepSeek',
    description: 'Nhà cung cấp các mô hình AI tiên tiến từ Trung Quốc',
    baseUrl: 'https://api.deepseek.com',
    apiVersion: 'v1',
    isActive: true,
    createdAt: '2024-01-15T10:30:00Z',
    models: ['DeepSeek Coder', 'DeepSeek Chat', 'DeepSeek Math'],
    maxTokens: 64000,
  },
  {
    id: '10',
    name: 'XAI',
    description: 'Grok AI từ xAI - Mô hình AI thông minh và hài hước',
    baseUrl: 'https://api.x.ai',
    apiVersion: 'v1',
    isActive: true,
    createdAt: '2024-02-20T14:15:00Z',
    models: ['Grok-1', 'Grok-1.5', 'Grok-2'],
    maxTokens: 128000,
  },
];

/**
 * Trang quản lý nhà cung cấp AI
 */
const AIProvidersPage: React.FC = () => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showModal, setShowModal] = useState(false);
  const [editingProvider, setEditingProvider] = useState<AIProvider | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showActionMenu, setShowActionMenu] = useState<string | null>(null);
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const formRef = useRef<any>(null);

  // Lọc danh sách nhà cung cấp theo từ khóa tìm kiếm và trạng thái
  const filteredProviders = mockAIProviders.filter(provider => {
    const matchesSearch =
      provider.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      provider.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === 'all' ||
      (statusFilter === 'active' && provider.isActive) ||
      (statusFilter === 'inactive' && !provider.isActive);

    return matchesSearch && matchesStatus;
  });

  // Cấu hình cột cho bảng
  const columns: TableColumn<AIProvider>[] = [
    {
      title: t('integration.ai.id', 'ID'),
      dataIndex: 'id',
      key: 'id',
      width: '5%',
    },
    {
      title: t('integration.ai.name', 'Tên nhà cung cấp'),
      dataIndex: 'name',
      key: 'name',
      width: '15%',
      render: (_: unknown, record: AIProvider) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <Icon
              name={getProviderIcon(record.name)}
              size="md"
              className="text-gray-600 dark:text-gray-300"
            />
          </div>
          <div className="flex-1 min-w-0">
            <Typography variant="body2" className="font-medium truncate">
              {record.name}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      title: t('integration.ai.description', 'Mô tả'),
      dataIndex: 'description',
      key: 'description',
      width: '18%',
    },
    {
      title: t('integration.ai.baseUrl', 'URL cơ sở'),
      dataIndex: 'baseUrl',
      key: 'baseUrl',
      width: '14%',
      render: (_: unknown, record: AIProvider) => (
        <a
          href={record.baseUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="text-primary hover:underline"
        >
          {record.baseUrl}
        </a>
      ),
    },
    {
      title: t('integration.ai.models', 'Mô hình hỗ trợ'),
      dataIndex: 'models',
      key: 'models',
      width: '15%',
      render: (_: unknown, record: AIProvider) => (
        <div className="max-h-20 overflow-y-auto">
          {record.models && record.models.length > 0 ? (
            <ul className="list-disc list-inside">
              {record.models.map((model, index) => (
                <li key={index} className="text-sm">
                  {model}
                </li>
              ))}
            </ul>
          ) : (
            <Typography variant="body2" color="muted" className="italic">
              {t('integration.ai.noData', 'Không có dữ liệu')}
            </Typography>
          )}
        </div>
      ),
    },
    {
      title: t('integration.ai.maxTokens', 'Max Tokens'),
      dataIndex: 'maxTokens',
      key: 'maxTokens',
      width: '8%',
      render: (_: unknown, record: AIProvider) => (
        <span>{record.maxTokens ? record.maxTokens.toLocaleString() : 'N/A'}</span>
      ),
    },
    {
      title: t('integration.ai.status', 'Trạng thái'),
      dataIndex: 'isActive',
      key: 'isActive',
      width: '10%',
      render: (_: unknown, record: AIProvider) => (
        <Badge variant={record.isActive ? 'success' : 'danger'} size="sm">
          {record.isActive
            ? t('integration.ai.active', 'Hoạt động')
            : t('integration.ai.inactive', 'Không hoạt động')}
        </Badge>
      ),
    },
    {
      title: t('integration.ai.actions', 'Hành động'),
      key: 'actions',
      width: '15%',
      render: (_: unknown, record: AIProvider) => (
        <div className="relative flex justify-center">
          <IconCard
            icon="menu"
            variant="ghost"
            size="sm"
            onClick={() => setShowActionMenu(record.id)}
          />
          {showActionMenu === record.id && (
            <div className="absolute z-50" style={{ left: '-100px' }}>
              <ModernMenu
                isOpen={showActionMenu === record.id}
                onClose={() => setShowActionMenu(null)}
                placement={
                  record.id === filteredProviders[filteredProviders.length - 1].id
                    ? 'top'
                    : 'bottom'
                }
                width="180px"
                items={[
                  {
                    id: 'edit',
                    label: t('common.edit', 'Sửa'),
                    icon: 'edit',
                    onClick: () => {
                      setEditingProvider(record);
                      setShowModal(true);
                      setShowActionMenu(null);
                    },
                  },
                  {
                    id: 'toggle',
                    label: record.isActive
                      ? t('integration.ai.disable', 'Vô hiệu hóa')
                      : t('integration.ai.enable', 'Kích hoạt'),
                    icon: record.isActive ? 'x' : 'check',
                    onClick: () => {
                      // Giả lập API call để cập nhật trạng thái
                      alert(
                        `${record.isActive ? 'Vô hiệu hóa' : 'Kích hoạt'} nhà cung cấp AI ${record.name}`
                      );
                      setShowActionMenu(null);
                    },
                  },
                  {
                    id: 'delete',
                    label: t('common.delete', 'Xóa'),
                    icon: 'trash',
                    onClick: () => {
                      setEditingProvider(record);
                      setShowDeleteConfirm(true);
                      setShowActionMenu(null);
                    },
                  },
                ]}
              />
            </div>
          )}
        </div>
      ),
    },
  ];

  // Xử lý khi submit form
  const handleSubmit = (data: Partial<AIProvider>) => {
    console.log('Form data:', data);
    // Giả lập API call
    alert(
      editingProvider
        ? `Đã cập nhật nhà cung cấp AI ${data.name}`
        : `Đã thêm nhà cung cấp AI ${data.name}`
    );
    setShowModal(false);
  };

  // Xử lý khi xác nhận xóa
  const handleDelete = () => {
    if (editingProvider) {
      // Giả lập API call
      alert(`Đã xóa nhà cung cấp AI ${editingProvider.name}`);
      setShowDeleteConfirm(false);
    }
  };

  // Xử lý khi click vào icon tìm kiếm
  const handleSearchClick = () => {
    setShowSearch(!showSearch);
    if (showSearch && searchTerm) {
      setSearchTerm('');
    }
  };

  // Xử lý khi chọn bộ lọc
  const handleFilterSelect = (value: string) => {
    setStatusFilter(value);
    setShowFilterMenu(false);
  };

  return (
    <Container>
      <Typography variant="h4" className="mb-4">
        {t('integration.ai.title', 'Quản lý nhà cung cấp AI')}
      </Typography>
      <Typography variant="body1" color="muted" className="mb-6">
        {t('integration.ai.description', 'Quản lý các nhà cung cấp AI và cấu hình kết nối')}
      </Typography>

      <div className="flex justify-between items-center mb-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3">
          <div className="flex items-center space-x-2">
            <Tooltip
              content={t('integration.ai.addProvider', 'Thêm nhà cung cấp AI')}
              position="bottom"
            >
              <IconCard
                icon="plus"
                variant="primary"
                onClick={() => {
                  setEditingProvider(null);
                  setShowModal(true);
                }}
              />
            </Tooltip>

            <Tooltip content={t('common.search', 'Tìm kiếm')} position="bottom">
              <IconCard
                icon="search"
                variant={showSearch ? 'primary' : 'default'}
                onClick={handleSearchClick}
                active={showSearch}
              />
            </Tooltip>

            <div className="relative">
              <Tooltip
                content={t('integration.ai.filterByStatus', 'Lọc theo trạng thái')}
                position="right"
              >
                <IconCard
                  icon="filter"
                  variant={statusFilter !== 'all' ? 'primary' : 'default'}
                  onClick={() => setShowFilterMenu(!showFilterMenu)}
                  active={statusFilter !== 'all'}
                />

                {showFilterMenu && (
                  <ModernMenu
                    isOpen={showFilterMenu}
                    onClose={() => setShowFilterMenu(false)}
                    placement="bottom"
                    width="180px"
                    items={[
                      {
                        id: 'all',
                        label: t('integration.ai.allStatuses', 'Tất cả trạng thái'),
                        icon: 'list',
                        onClick: () => handleFilterSelect('all'),
                      },
                      {
                        id: 'active',
                        label: t('integration.ai.active', 'Đang hoạt động'),
                        icon: 'check-circle',
                        onClick: () => handleFilterSelect('active'),
                      },
                      {
                        id: 'inactive',
                        label: t('integration.ai.inactive', 'Không hoạt động'),
                        icon: 'x-circle',
                        onClick: () => handleFilterSelect('inactive'),
                      },
                    ]}
                  />
                )}
              </Tooltip>
            </div>
          </div>

          <div className="w-full sm:w-auto">
            <SearchBar
              visible={showSearch}
              value={searchTerm}
              onChange={setSearchTerm}
              onToggle={handleSearchClick}
              maxWidth="100%"
              variant="flat"
              autoFocus={true}
              showSearchIcon={false}
              className="w-full"
              placeholder={t('integration.ai.searchPlaceholder', 'Tìm kiếm nhà cung cấp...')}
            />
          </div>
        </div>
      </div>

      <Card className="mb-6 p-6">
        <div className="mb-4">
          <Typography variant="h5">
            {t('integration.ai.providersList', 'Danh sách nhà cung cấp AI')}
          </Typography>
        </div>

        <Table columns={columns} data={filteredProviders} rowKey="id" pagination />
      </Card>

      {/* Modal thêm/sửa nhà cung cấp AI */}
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title={
          editingProvider
            ? t('integration.ai.editProvider', 'Sửa nhà cung cấp AI')
            : t('integration.ai.addProvider', 'Thêm nhà cung cấp AI')
        }
        size="lg"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setShowModal(false)}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button type="submit" form="aiProviderForm" variant="primary">
              {editingProvider ? t('common.update', 'Cập nhật') : t('common.add', 'Thêm mới')}
            </Button>
          </div>
        }
      >
        <Form
          id="aiProviderForm"
          onSubmit={handleSubmit}
          ref={formRef}
          defaultValues={
            editingProvider
              ? {
                  name: editingProvider.name,
                  description: editingProvider.description,
                  baseUrl: editingProvider.baseUrl,
                  apiVersion: editingProvider.apiVersion,
                  isActive: editingProvider.isActive,
                }
              : {
                  name: '',
                  description: '',
                  baseUrl: '',
                  apiVersion: '',
                  isActive: true,
                }
          }
        >
          <FormGrid columns={2} columnsSm={1} gap="md">
            <FormItem name="name" label={t('integration.ai.name', 'Tên nhà cung cấp')} required>
              <Input
                placeholder={t('integration.ai.namePlaceholder', 'Nhập tên nhà cung cấp AI')}
                fullWidth
              />
            </FormItem>

            <FormItem name="baseUrl" label={t('integration.ai.baseUrl', 'URL cơ sở')} required>
              <Input
                placeholder={t('integration.ai.baseUrlPlaceholder', 'Nhập URL cơ sở API')}
                fullWidth
              />
            </FormItem>

            <FormItem name="description" label={t('integration.ai.description', 'Mô tả')}>
              <Input
                placeholder={t('integration.ai.descriptionPlaceholder', 'Nhập mô tả')}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="apiVersion"
              label={t('integration.ai.apiVersion', 'Phiên bản API')}
              required
            >
              <Input
                placeholder={t('integration.ai.apiVersionPlaceholder', 'Nhập phiên bản API')}
                fullWidth
              />
            </FormItem>

            <FormItem name="isActive" label={t('integration.ai.status', 'Trạng thái')} inline>
              <Toggle />
            </FormItem>
          </FormGrid>
        </Form>
      </Modal>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        title={t('common.confirmDelete', 'Xác nhận xóa')}
        size="sm"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setShowDeleteConfirm(false)}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button variant="danger" onClick={handleDelete}>
              {t('common.delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography>
            {t(
              'integration.ai.confirmDeleteMessage',
              'Bạn có chắc chắn muốn xóa nhà cung cấp AI {{name}}?',
              {
                name: editingProvider?.name,
              }
            )}
          </Typography>
        </div>
      </Modal>
    </Container>
  );
};

export default AIProvidersPage;
